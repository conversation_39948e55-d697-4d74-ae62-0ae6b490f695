import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/css/about-us.css',
                'resources/css/booking-calendar.css',
                'resources/css/contact.css',
                'resources/css/creator.css',
                'resources/css/kol-item.css',
                'resources/css/kol-list.css',
                'resources/css/layout.css',
                'resources/css/livewire-product-list.css',
                'resources/css/payment.css',
                'resources/css/process.css',
                'resources/css/product-detail.css',
                'resources/css/product-filter.css',
                'resources/css/setting.css',
                'resources/css/index.css',

                'resources/js/app.js',
                'resources/js/layout.js',
                'resources/js/product-filter.js',
                'resources/js/process.js',
                'resources/js/about-us.js',
            ],
            refresh: true,
        }),
        tailwindcss(),
    ],
});
