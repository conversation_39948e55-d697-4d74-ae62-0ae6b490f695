<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('kols', function (Blueprint $table) {
            // Thêm trường giới tính
            $table->enum('gender', ['male', 'female', 'other'])->nullable()->after('name');

            // Thêm các trường bổ sung từ trang product-detail
            $table->string('nickname')->nullable()->after('name');
            $table->json('categories')->nullable()->after('expertise');
            $table->text('achievements')->nullable()->after('bio');
            $table->json('gallery')->nullable()->after('avatar');
            $table->json('schedule')->nullable()->after('social_links');
            $table->string('response_time')->nullable()->after('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('kols', function (Blueprint $table) {
            $table->dropColumn([
                'gender',
                'nickname',
                'categories',
                'achievements',
                'gallery',
                'schedule',
                'response_time',
            ]);
        });
    }
};
