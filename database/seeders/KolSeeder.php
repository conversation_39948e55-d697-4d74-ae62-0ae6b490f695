<?php

namespace Database\Seeders;

use App\Models\Kol;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class KolSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        // Danh sách tên KOL mẫu - cập nhật với nhiều tên Việt Nam phổ biến hơn
        $firstNames = [
            '<PERSON>', 'H<PERSON>', '<PERSON>h', 'Tuấn', '<PERSON><PERSON>ền', 'Thà<PERSON>', '<PERSON><PERSON><PERSON>', 'H<PERSON>ơng', '<PERSON><PERSON>', 'Th<PERSON><PERSON>',
            '<PERSON>u<PERSON>', '<PERSON>rang', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>ơ<PERSON>', '<PERSON>', 'Lan', '<PERSON>ả<PERSON>', 'Yến', 'Tùng',
            '<PERSON>h', 'Th<PERSON>', 'Hiền', 'Hùng', 'Thắng', 'Thi', '<PERSON>h<PERSON><PERSON>', 'Th<PERSON><PERSON>', '<PERSON>r<PERSON>', '<PERSON>h<PERSON><PERSON>',
            '<PERSON>h<PERSON><PERSON>', '<PERSON>hắ<PERSON>', '<PERSON>h<PERSON><PERSON>', '<PERSON>h<PERSON>ơ<PERSON>', 'Th<PERSON><PERSON>', 'Thần', '<PERSON>h<PERSON><PERSON>', 'Th<PERSON><PERSON>', 'Th<PERSON><PERSON>', 'Thắng',
        ];

        $lastNames = [
            'Nguyễn', 'Trần', 'Lê', 'Phạm', 'Hoàng', 'Huỳnh', 'Đặng', 'Võ', 'Bùi', 'Đỗ',
            'Hồ', 'Ngô', 'Dương', 'Lý', 'Đinh', 'Trịnh', 'Trương', 'Vũ', 'Tạ', 'Phùng',
            'Mai', 'Cao', 'Lương', 'Lưu', 'Tôn', 'Thái', 'Trần', 'Trịnh', 'Văn', 'Vương',
        ];

        $nicknames = [
            'Sky', 'Star', 'Moon', 'Sun', 'Cloud', 'Rain', 'Wind', 'Fire', 'Water', 'Earth',
            'Light', 'Dark', 'Shadow', 'Dream', 'Hope', 'Love', 'Joy', 'Peace', 'Soul', 'Spirit',
            'Angel', 'Devil', 'Dragon', 'Tiger', 'Lion', 'Eagle', 'Wolf', 'Fox', 'Bear', 'Panda',
            'Ruby', 'Sapphire', 'Emerald', 'Diamond', 'Pearl', 'Crystal', 'Jade', 'Amber', 'Coral', 'Opal',
            'Phoenix', 'Unicorn', 'Mermaid', 'Fairy', 'Wizard', 'Knight', 'Ninja', 'Samurai', 'Pirate', 'Viking',
        ];

        $categories = [
            'Game thủ', 'Streamer', 'Idol', 'Cosplayer', 'Người mẫu', 'Ca sĩ', 'Diễn viên', 'Vũ công',
            'Nghệ sĩ', 'KOL', 'Influencer', 'Content Creator', 'Reviewer', 'Blogger', 'Vlogger',
            'Tiktoker', 'Youtuber', 'Nhiếp ảnh gia', 'Makeup Artist', 'Fashionista', 'Foodie', 'Travel Blogger',
            'Fitness Trainer', 'Gamer', 'Esports Player', 'Comedian', 'Dancer', 'Singer', 'Musician', 'Artist',
        ];

        $expertiseAreas = [
            'Gaming', 'Streaming', 'Beauty', 'Fashion', 'Food', 'Travel', 'Fitness', 'Music', 'Dance', 'Acting',
            'Comedy', 'Lifestyle', 'Technology', 'Education', 'Health', 'Business', 'Finance', 'Sports', 'Art', 'Photography',
            'Makeup', 'Cooking', 'DIY', 'Crafts', 'Home Decor', 'Parenting', 'Pets', 'Automotive', 'Science', 'History',
        ];

        $achievements = [
            'Top 10 streamer năm 2023',
            'Giải thưởng KOL xuất sắc',
            '1 triệu người theo dõi trên TikTok',
            '500 nghìn người đăng ký kênh YouTube',
            'Top 1 game thủ PUBG Mobile',
            'Quán quân cuộc thi hát',
            'Giải nhất cuộc thi nhảy',
            'Diễn viên xuất sắc nhất',
            'KOL được yêu thích nhất',
            'Người mẫu ảnh nổi tiếng',
            'Influencer có tầm ảnh hưởng lớn',
            'Top 5 content creator',
            'Reviewer sản phẩm uy tín',
            'Blogger du lịch nổi tiếng',
            'Vlogger được yêu thích',
            'Giải thưởng TikTok Creator năm 2023',
            'Top 10 Influencer có tầm ảnh hưởng lớn nhất',
            'Giải thưởng Streamer mới xuất sắc',
            'Kỷ lục Guinness về số giờ livestream liên tục',
            'Giải thưởng Người mẫu ảnh của năm',
            'Hợp đồng quảng cáo với các thương hiệu lớn',
            'Giải thưởng Video sáng tạo nhất',
            'Top 3 Cosplayer được yêu thích nhất',
            'Giải thưởng Nghệ sĩ đa năng',
            'Kỷ lục về số lượng người xem trực tiếp',
        ];

        // Tạo 50 KOL mẫu
        for ($i = 1; $i <= 50; $i++) {
            $firstName = $firstNames[array_rand($firstNames)];
            $lastName = $lastNames[array_rand($lastNames)];
            $name = $lastName.' '.$firstName;
            $nickname = $nicknames[array_rand($nicknames)].rand(10, 99);
            $gender = ['male', 'female', 'other'][rand(0, 2)];

            // Chọn ngẫu nhiên 2-4 danh mục
            $kolCategories = [];
            $numCategories = rand(2, 4);
            $shuffledCategories = $categories;
            shuffle($shuffledCategories);
            for ($j = 0; $j < $numCategories; $j++) {
                $kolCategories[] = $shuffledCategories[$j];
            }

            // Chọn ngẫu nhiên 1-3 thành tích
            $kolAchievements = [];
            $numAchievements = rand(1, 3);
            $shuffledAchievements = $achievements;
            shuffle($shuffledAchievements);
            for ($j = 0; $j < $numAchievements; $j++) {
                $kolAchievements[] = $shuffledAchievements[$j];
            }

            // Chọn ngẫu nhiên 2-3 lĩnh vực chuyên môn
            $kolExpertise = [];
            $numExpertise = rand(2, 3);
            $shuffledExpertise = $expertiseAreas;
            shuffle($shuffledExpertise);
            for ($j = 0; $j < $numExpertise; $j++) {
                $kolExpertise[] = $shuffledExpertise[$j];
            }

            // Tạo bio chi tiết hơn
            $bioTemplates = [
                'Xin chào, tôi là {name} ({nickname}). Tôi là {categories} với {experience} năm kinh nghiệm. Chuyên môn của tôi là {expertise}. Rất vui được gặp bạn!',
                '{name} đây, có thể bạn biết tôi với biệt danh {nickname}. Tôi hoạt động trong lĩnh vực {categories} và đặc biệt yêu thích {expertise}. Hãy liên hệ với tôi để cùng tạo nên những nội dung tuyệt vời!',
                'Tôi là {nickname} ({name}), một {categories} đam mê với {expertise}. Với kinh nghiệm {experience} năm, tôi tự tin mang đến cho bạn những trải nghiệm tuyệt vời nhất.',
                'Chào bạn, {name} đây! Tôi là {categories} với biệt danh {nickname}. Tôi đã có {experience} năm kinh nghiệm trong lĩnh vực {expertise}. Hãy để tôi giúp bạn tỏa sáng!',
            ];

            $bioTemplate = $bioTemplates[array_rand($bioTemplates)];
            $experience = rand(1, 10);
            $bio = str_replace(
                ['{name}', '{nickname}', '{categories}', '{expertise}', '{experience}'],
                [$name, $nickname, implode(', ', $kolCategories), implode(', ', $kolExpertise), $experience],
                $bioTemplate
            );

            // Tạo KOL với dữ liệu phù hợp hơn
            $kol = Kol::create([
                'name' => $name,
                'nickname' => $nickname,
                'gender' => $gender,
                'email' => strtolower(Str::slug($nickname)).'@example.com',
                'phone' => '0'.rand(900000000, 999999999),
                'bio' => $bio,
                'expertise' => $kolExpertise, // Đổi thành mảng theo định nghĩa model
                'categories' => $kolCategories,
                'achievements' => implode("\n", $kolAchievements),
                'hourly_rate' => rand(10, 30) * 10000,
                'is_active' => (rand(1, 10) > 2), // 80% KOL đang hoạt động
                'social_links' => [
                    [
                        'platform' => 'facebook',
                        'url' => 'https://facebook.com/'.Str::slug($nickname),
                    ],
                    [
                        'platform' => 'instagram',
                        'url' => 'https://instagram.com/'.Str::slug($nickname),
                    ],
                    [
                        'platform' => 'tiktok',
                        'url' => 'https://tiktok.com/@'.Str::slug($nickname),
                    ],
                    [
                        'platform' => 'youtube',
                        'url' => 'https://youtube.com/@'.Str::slug($nickname),
                    ],
                ],
                'schedule' => [
                    [
                        'days' => 'Thứ 2 - Thứ 6',
                        'hours' => '19:00 - 23:00',
                    ],
                    [
                        'days' => 'Thứ 7 - Chủ nhật',
                        'hours' => '10:00 - 22:00',
                    ],
                ],
            ]);

            // Không cần gán dịch vụ vì bảng services đã bị xóa

            // Tạo lịch làm việc cho KOL
            for ($j = 0; $j < 7; $j++) {
                $date = now()->addDays($j)->format('Y-m-d');

                // 70% có lịch làm việc vào ngày này
                if (rand(1, 10) <= 7) {
                    DB::table('schedules')->insert([
                        'kol_id' => $kol->id,
                        'date' => $date,
                        'start_time' => rand(10, 19).':00:00',
                        'end_time' => rand(20, 23).':00:00',
                        'is_available' => (rand(1, 10) > 2), // 80% có sẵn
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }
    }
}
