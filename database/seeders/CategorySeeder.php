<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            ['name' => 'Game thủ', 'color' => '#FF6B6B', 'sort_order' => 1],
            ['name' => 'Streamer', 'color' => '#4ECDC4', 'sort_order' => 2],
            ['name' => 'Idol', 'color' => '#45B7D1', 'sort_order' => 3],
            ['name' => 'Cosplayer', 'color' => '#96CEB4', 'sort_order' => 4],
            ['name' => 'Người mẫu', 'color' => '#FFEAA7', 'sort_order' => 5],
            ['name' => 'Ca sĩ', 'color' => '#DDA0DD', 'sort_order' => 6],
            ['name' => 'Diễn viên', 'color' => '#98D8C8', 'sort_order' => 7],
            ['name' => 'Vũ công', 'color' => '#F7DC6F', 'sort_order' => 8],
            ['name' => 'Nghệ sĩ', 'color' => '#BB8FCE', 'sort_order' => 9],
            ['name' => 'KOL', 'color' => '#85C1E9', 'sort_order' => 10],
            ['name' => 'Influencer', 'color' => '#F8C471', 'sort_order' => 11],
            ['name' => 'Content Creator', 'color' => '#82E0AA', 'sort_order' => 12],
            ['name' => 'Reviewer', 'color' => '#F1948A', 'sort_order' => 13],
            ['name' => 'Blogger', 'color' => '#C39BD3', 'sort_order' => 14],
            ['name' => 'Vlogger', 'color' => '#7FB3D3', 'sort_order' => 15],
        ];

        foreach ($categories as $category) {
            Category::create([
                'name' => $category['name'],
                'slug' => Str::slug($category['name']),
                'color' => $category['color'],
                'sort_order' => $category['sort_order'],
                'is_active' => true,
            ]);
        }
    }
}
