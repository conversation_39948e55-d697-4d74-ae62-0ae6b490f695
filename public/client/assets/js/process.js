// JavaScript cho trang quy trình hoạt động
document.addEventListener('DOMContentLoaded', function() {
    // L<PERSON>y các phần tử cần thiết
    const tabs = document.querySelectorAll('.tab-item');
    const sections = document.querySelectorAll('.process-step');
    const tabsContainer = document.querySelector('.process-tabs');

    // Thêm sự kiện click cho các tab
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Lấy ID của section tương ứng
            const targetId = this.getAttribute('data-target');
            const targetSection = document.getElementById(targetId);

            // Scroll đến section tương ứng
            if (targetSection) {
                window.scrollTo({
                    top: targetSection.offsetTop - tabsContainer.offsetHeight,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Xử lý active tab khi scroll
    window.addEventListener('scroll', function() {
        // Tab đã được thiết lập sticky bằng CSS

        // Xử lý active tab khi scroll
        let currentSection = '';

        sections.forEach(section => {
            const sectionTop = section.offsetTop - tabsContainer.offsetHeight - 10;
            const sectionHeight = section.offsetHeight;

            if (window.pageYOffset >= sectionTop && window.pageYOffset < sectionTop + sectionHeight) {
                currentSection = section.getAttribute('id');
            }
        });

        // Cập nhật active tab
        if (currentSection) {
            tabs.forEach(tab => {
                tab.classList.remove('active');
                if (tab.getAttribute('data-target') === currentSection) {
                    tab.classList.add('active');
                }
            });
        }
    });

    // Kích hoạt scroll event ngay khi trang tải xong để cập nhật active tab
    window.dispatchEvent(new Event('scroll'));
});
