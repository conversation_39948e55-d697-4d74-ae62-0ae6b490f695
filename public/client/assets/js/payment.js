// JavaScript cho trang thanh toán
document.addEventListener('DOMContentLoaded', function() {
    // X<PERSON> lý chọn phương thức thanh toán
    const methodOptions = document.querySelectorAll('.method-option');
    
    methodOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Bỏ active tất cả các options
            methodOptions.forEach(opt => {
                opt.classList.remove('active');
                opt.querySelector('input').checked = false;
            });
            
            // Active option được chọn
            this.classList.add('active');
            this.querySelector('input').checked = true;
        });
    });
    
    // Xử lý form thanh toán
    const paymentForm = document.getElementById('payment-form');
    if (paymentForm) {
        paymentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Kiểm tra thông tin
            const fullName = document.getElementById('fullName').value;
            const phone = document.getElementById('phone').value;
            const email = document.getElementById('email').value;
            
            if (!fullName || !phone || !email) {
                alert('Vui lòng điền đầy đủ thông tin khách hàng');
                return;
            }
            
            // Hiển thị thông báo thanh toán
            alert('Vui lòng chuyển khoản theo thông tin bên dưới để hoàn tất đặt lịch');
            
            // Ở đây có thể thêm code để gửi thông tin đến server
        });
    }
    
    // Xử lý nút xác nhận đặt hàng
    const confirmButton = document.getElementById('confirm-order');
    if (confirmButton) {
        confirmButton.addEventListener('click', function() {
            // Kiểm tra xem đã thanh toán chưa
            const paymentStatus = document.querySelector('.payment-status');
            if (paymentStatus) {
                alert('Vui lòng hoàn tất thanh toán trước khi xác nhận đặt hàng');
            }
        });
    }
});
