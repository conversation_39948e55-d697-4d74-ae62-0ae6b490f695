document.addEventListener("DOMContentLoaded", (event) => {
    gsap.registerPlugin(ScrollTrigger);
    
    gsap.to(".gasp_wrap", {
        top: "50%",
        scale: 1,
        transform: "translate(-50%, -50%)",
        // ease: "power2.inOut",  
        scrollTrigger: {
            trigger: ".logo_video_wrap",
            start: "20% 0",
            end: "98% 100%",
            // scrub: true,
            scrub: 1,
            // markers: true
        },
    });

    gsap.to(".black_cover", {
        opacity: 1,
        scrollTrigger: {
            trigger: ".photo_text_section",
            start: "-20% 0",
            end: "40% 100%",
            // scrub: true,
            scrub: 1,
            // markers: true
        },
    });

    // trendy_text
    let tl = gsap.timeline({
        scrollTrigger: {
            trigger: ".photo_text_section",
            start: "0% 0",
            end: "60% 100%",
            // scrub: true,
            scrub: 1,
            // markers: true
        }
    });

    tl.to(".trendy_text", {
        opacity: 1,
        transform: "translateY(0%)",
        duration: 1,
    });

    tl.to({}, { duration: 1 });

    tl.to(".trendy_text", {
        opacity: 0,
        transform: "translateY(-50%)",
        duration: 1,
    });


    gsap.to(".desc_text", {
        opacity: 1,
        transform: "translateY(0%)",
        scrollTrigger: {
            trigger: ".photo_text_section",
            start: "50% 0",
            end: "100% 100%",
            scrub: 1,
            // scrub: true,
            // markers: true
        },
    });


});