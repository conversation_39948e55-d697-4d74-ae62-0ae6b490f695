// JavaScript cho slideshow ý kiến khách hàng
let testimonialIndex = 0;
const testimonialItems = document.querySelectorAll('.testimonial-item');
const testimonialDots = document.querySelectorAll('.testimonial-dot');

// Tự động chuyển slide sau mỗi 5 giây
let testimonialTimer = setInterval(function() {
    moveTestimonial(1);
}, 5000);

// Hàm chuyển slide
function moveTestimonial(n) {
    testimonialIndex += n;
    
    // Xử lý khi đến slide cuối cùng hoặc đầu tiên
    if (testimonialIndex >= testimonialItems.length) {
        testimonialIndex = 0;
    } else if (testimonialIndex < 0) {
        testimonialIndex = testimonialItems.length - 1;
    }
    
    showTestimonial();
    resetTimer();
}

// Hàm hiển thị slide theo index
function currentTestimonial(n) {
    testimonialIndex = n;
    showTestimonial();
    resetTimer();
}

// Hàm hiển thị slide hiện tại và ẩn các slide khác
function showTestimonial() {
    // Ẩn tất cả các slide
    for (let i = 0; i < testimonialItems.length; i++) {
        testimonialItems[i].classList.remove('active');
        testimonialDots[i].classList.remove('active');
    }
    
    // Hiển thị slide hiện tại
    testimonialItems[testimonialIndex].classList.add('active');
    testimonialDots[testimonialIndex].classList.add('active');
}

// Reset timer khi người dùng tương tác
function resetTimer() {
    clearInterval(testimonialTimer);
    testimonialTimer = setInterval(function() {
        moveTestimonial(1);
    }, 5000);
}

// Khởi tạo slideshow khi trang được tải
document.addEventListener('DOMContentLoaded', function() {
    // Đảm bảo các phần tử đã được tải
    if (testimonialItems.length > 0 && testimonialDots.length > 0) {
        showTestimonial();
    }
});
