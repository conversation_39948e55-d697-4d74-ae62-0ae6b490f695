
function animateNumber(element, start, end, duration) {
    let startTime = null;

    function animation(currentTime) {
        if (!startTime) startTime = currentTime;
        const elapsed = currentTime - startTime;

        // Easing function for smooth animation (ease out)
        const easeOutCubic = (t) => (--t) * t * t + 1;

        const progress = Math.min(elapsed / duration, 1);
        const easedProgress = easeOutCubic(progress);
        const currentNumber = Math.floor(start + (end - start) * easedProgress);

        element.textContent = currentNumber;

        if (progress < 1) {
            requestAnimationFrame(animation);
        } else {
            element.textContent = end;
        }
    }

    requestAnimationFrame(animation);
}


function checkVisibilityAndTriggerAnimation() {
    const numberSection = document.querySelector('.number_section');
    const numbers = document.querySelectorAll('.number');



    const sectionTop = numberSection.getBoundingClientRect().top;
    const sectionBottom = numberSection.getBoundingClientRect().bottom;
    const triggerPoint = window.innerHeight;


    if (sectionBottom >= 0 && sectionTop <= triggerPoint) {
        numbers.forEach(number => {
            const targetNumber = parseInt(number.getAttribute('data-target'), 10);
            animateNumber(number, 0, targetNumber, 2500);
        });

        window.removeEventListener('scroll', checkVisibilityAndTriggerAnimation);
    }
}

window.addEventListener('load', () => {
    checkVisibilityAndTriggerAnimation();
});

window.addEventListener('scroll', checkVisibilityAndTriggerAnimation);



// 커서 이펙트
// document.addEventListener('DOMContentLoaded', () => {
//     const cursor = document.querySelector('.cursor_effect');

//     if (cursor) {
//         let mouseX = 0, mouseY = 0;
//         let posX = 0, posY = 0;
//         const cursorWidth = 888;
//         const cursorHeight = 888;

//         document.addEventListener('mousemove', (e) => {
//             mouseX = e.clientX;
//             mouseY = e.clientY;
//         });

//         function animate() {
//             posX += (mouseX - posX) * 0.1;
//             posY += (mouseY - posY) * 0.1;

//             cursor.style.transform = `translate(${posX - cursorWidth / 2}px, ${posY - cursorHeight / 2}px)`;

//             requestAnimationFrame(animate);
//         }

//         animate();
//     } else {
//         console.error("Cursor element not found");
//     }
// });



document.addEventListener("DOMContentLoaded", function () {
    const maincreatorList = document.getElementById("maincreatorList");
    let creatorHtml = "";

    creatorData.forEach((result) => {
        creatorHtml += `
            <li class="creator_slide">
                <div class="hover_wrap">
                    <div class="name_btn_wrap">
                        <p class="name eng_font font_bold font_pink">${result.name}</p>
                        <div class="btn_wrap">
                            ${result.tiktok ? `<a href="${result.tiktok}" class="leave" target="_blank">
                                <img src="/client/assets/img/creator/link_icon/tiktok.svg" alt="Tiktok">
                            </a>` : ""}
                            ${result.insta ? `<a href="${result.insta}" class="leave" target="_blank">
                                <img src="/client/assets/img/creator/link_icon/insta.svg" alt="Instagram">
                            </a>` : ""}
                            ${result.youtube ? `<a href="${result.youtube}" class="leave" target="_blank">
                                <img src="/client/assets/img/creator/link_icon/youtube.svg" alt="YouTube">
                            </a>` : ""}
                        </div>
                    </div>
                    <img src="${result.img}" alt="${result.name}">
                </div>
            </li>
        `;
    });

    maincreatorList.innerHTML = creatorHtml;

    const swiperWrapper = document.querySelector('.creator_wrapper');


    const clone = swiperWrapper.cloneNode(true);
    swiperWrapper.appendChild(clone);

    const logoWrapper = document.querySelector('.swiper-wrapper');

    const logoclone = logoWrapper.cloneNode(true);
    logoWrapper.appendChild(logoclone);
});
