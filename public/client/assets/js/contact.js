document.addEventListener('DOMContentLoaded', function () {
    const brandsButton = document.getElementById('brandsButton')
    const creatorsButton = document.getElementById('creatorsButton')
    const fotBrands = document.querySelector('.for_brands')
    const fotCreators = document.querySelector('.for_creators')
    const brandSubmit = document.getElementById('brandSubmit')
    const creatorSubmit = document.getElementById('creatorSubmit')

    brandsButton.addEventListener('click', function () {
        brandsButton.classList.add('on')
        creatorsButton.classList.remove('on')

        fotBrands.style.display = 'block'
        fotCreators.style.display = 'none'
    })

    creatorsButton.addEventListener('click', function () {
        creatorsButton.classList.add('on')
        brandsButton.classList.remove('on')

        fotCreators.style.display = 'block'
        fotBrands.style.display = 'none'
    })

    brandSubmit.addEventListener('click', function () {
        submit('brand')
    })

    creatorSubmit.addEventListener('click', function () {
        submit('creator')
    })

    async function submit(type) {
        let brand_name = document.getElementById('brand_name').value
        let brand_email = document.getElementById('brand_email').value
        let brand_phone = document.getElementById('brand_phone').value
        let brand_company = document.getElementById('brand_company').value
        let brand_help = document.getElementById('brand_help').value
        let brand_hear = document.getElementById('brand_hear').value
        let brand_news = document.getElementById('brand_news').checked
        let creator_name = document.getElementById('creator_name').value
        let creator_email = document.getElementById('creator_email').value
        let creator_phone = document.getElementById('creator_phone').value
        let creator_birth = document.getElementById('creator_birth').value
        let creator_gender = document.querySelector(
            'input[name="creator_gender"]:checked'
        ).value
        let creator_link = document.getElementById('creator_link').value
        let creator_reason = document.getElementById('creator_reason').value
        let creator_hear = document.getElementById('creator_hear').value
        let data = null
        if (type == 'brand') {
            data = {
                type: type,
                name: brand_name,
                email: brand_email,
                contact_number: brand_phone,
                company: brand_company,
                how_can_we_help: brand_help,
                how_did_you_hear: brand_hear,
                newsletter_signup: brand_news,
            }
            if (!data.name || !data.email || !data.contact_number || !data.company || !data.how_can_we_help || !data.how_did_you_hear) {
                alert('Please fill necessary fields')
                return
            }
        } else if (type == 'creator') {
            data = {
                type: type,
                name: creator_name,
                email: creator_email,
                contact_number: creator_phone,
                date_of_birth: creator_birth,
                gender: creator_gender,
                social_media_link: creator_link,
                reason_for_applying: creator_reason,
                how_did_you_hear: creator_hear,
            }
            if (!data.name || !data.email || !data.contact_number || !data.date_of_birth || !data.gender || !data.social_media_link || !data.reason_for_applying) {
                alert('Please fill necessary fields')
                return
            }
        }
        const response = await fetch(
            "https://email.teamcredit.kr/api/send-email/pi-corp",
            {
              method: "POST",
              headers: {
                Accept: "application/json",
                "Content-Type": "application/json",
              },
              body: JSON.stringify(data),
            }
          ).then((response) => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
              }
              alert('Thank you for your submission')
            window.location.reload();
          }).catch((error) => {
            alert('An error occurred, please try again later');
            console.error('Error:', error);
          });
        console.log(data)
    }
})
