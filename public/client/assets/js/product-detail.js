// Gallery Slideshow functionality
let gallerySlideIndex = 0;
const gallerySlides = document.querySelectorAll('.gallery-section .slide');
const galleryDots = document.querySelectorAll('.gallery-section .dot');

function showGallerySlides(n) {
    // Reset all slides and dots
    for (let i = 0; i < gallerySlides.length; i++) {
        gallerySlides[i].classList.remove('active');
        galleryDots[i].classList.remove('active-dot');
    }
    
    // Set active slide and dot
    gallerySlides[n].classList.add('active');
    galleryDots[n].classList.add('active-dot');
    gallerySlideIndex = n;
}

function changeSlide(n) {
    let newIndex = gallerySlideIndex + n;
    if (newIndex >= gallerySlides.length) {
        newIndex = 0;
    } else if (newIndex < 0) {
        newIndex = gallerySlides.length - 1;
    }
    showGallerySlides(newIndex);
}

function currentSlide(n) {
    showGallerySlides(n);
}

// Header Slideshow functionality
let headerSlideIndex = 0;
const headerSlides = document.querySelectorAll('.product-slideshow .slide');
const headerDots = document.querySelectorAll('.product-slideshow .slide-dot');

function showHeaderSlides(n) {
    // Reset all slides and dots
    for (let i = 0; i < headerSlides.length; i++) {
        headerSlides[i].classList.remove('active');
        headerDots[i].classList.remove('active');
    }
    
    // Set active slide and dot
    headerSlides[n].classList.add('active');
    headerDots[n].classList.add('active');
    headerSlideIndex = n;
}

function changeHeaderSlide(n) {
    let newIndex = headerSlideIndex + n;
    if (newIndex >= headerSlides.length) {
        newIndex = 0;
    } else if (newIndex < 0) {
        newIndex = headerSlides.length - 1;
    }
    showHeaderSlides(newIndex);
}

function currentHeaderSlide(n) {
    showHeaderSlides(n);
}

// Auto slideshows
setInterval(() => {
    changeSlide(1);
}, 5000);

setInterval(() => {
    changeHeaderSlide(1);
}, 4000);

// Modal functionality
const modal = document.getElementById('bookingModal');
const bookingBtn = document.getElementById('bookingBtn');
const closeModal = document.getElementById('closeModal');

bookingBtn.addEventListener('click', () => {
    modal.classList.add('show');
});

closeModal.addEventListener('click', () => {
    modal.classList.remove('show');
});

// Close modal when clicking outside
window.addEventListener('click', (event) => {
    if (event.target === modal) {
        modal.classList.remove('show');
    }
});

// Calendar day selection
const calendarDays = document.querySelectorAll('.calendar-day:not(.disabled)');
calendarDays.forEach(day => {
    day.addEventListener('click', () => {
        // Remove active class from all days
        calendarDays.forEach(d => d.classList.remove('active'));
        // Add active class to clicked day
        day.classList.add('active');
        // Update time slots title
        document.querySelector('.time-slot-title').textContent = `Khung giờ ngày ${day.textContent}/07/2023`;
    });
});

// Time slot selection
const timeSlots = document.querySelectorAll('.time-slot:not(.disabled)');
timeSlots.forEach(slot => {
    slot.addEventListener('click', () => {
        // Remove active class from all slots
        timeSlots.forEach(s => s.classList.remove('active'));
        // Add active class to clicked slot
        slot.classList.add('active');
    });
});
