.contact_section {
    margin-top: 76px;
    margin-top: 120px;
    padding-bottom: 76px;
    display: flex;
    align-items: center;
    justify-content: center;
    /* padding-top: 80px; */
    min-height: calc(100vh);
}

.contact_section .left {
    width: 600px;
    height: 718px;
}

.contact_section .left video {
    width: 100%;

}

.contact_section .right {
    width: calc(100% - 600px);
    padding: 0 36px;
}

.contact_section .right .button_wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 42px;
}

.contact_section .right .button_wrap button {
    height: 44px;
    border-radius: 44px;
    color: #fff;
    border: 1px solid #ffffff30;
    width: 160px;
    font-size: 16px;
    cursor: pointer;
}

.contact_section .right .button_wrap button.on {
    background-color: #fff;
    border: 1px solid #fff;
    color: #000;
}

.contact_section .right .button_wrap button:first-child {
    margin-right: 10px;
}

.contact_section .right .input_wrap.for_creators {
    display: none;
}

.contact_section .right .input_wrap>ul {
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
}

.contact_section .right .input_wrap>ul>li {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.contact_section .right .input_wrap>ul>li:not(:last-child) {
    margin-bottom: 20px;
}

.contact_section .right .input_wrap>ul>li>label {
    color: #fff;
    font-size: 14px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.contact_section .right .input_wrap>ul>li.essential>label::after {
    content: '*';
    display: flex;
    align-items: center;
    margin-left: 4px;
    color: var(--mainpink);
}

.contact_section .right .input_wrap>ul>li>input[type="text"] {
    width: 100%;
    border: 1px solid #ffffff30;
    border-radius: 3px;
    height: 46px;
    color: #fff;
    padding: 0 14px;
}

.contact_section .right .input_wrap>ul>li>input[type="text"]::placeholder {
    font-family: "Montserrat", sans-serif;
    font-size: 14px;
    font-weight: 600;
    color: #ffffff40;
}

.contact_section .right .input_wrap>ul>li>input:focus {
    border: 1px solid #ffffff;
}

.contact_section .right .input_wrap>ul>li>.radio_wrap {
    display: flex;
    align-items: center;
}

.contact_section .right .input_wrap>ul>li>.radio_wrap li {
    display: flex;
    align-items: center;
}

.contact_section .right .input_wrap>ul>li>.radio_wrap li input[type="radio"] {
    display: none;
}

.contact_section .right .input_wrap>ul>li>.radio_wrap li label {
    color: #fff;
    display: flex;
    align-items: center;
    font-size: 14px;
    padding-left: 28px;
    background-image: url(../img/radio.svg);
    height: 18px;
    background-position: left center;
    background-repeat: no-repeat;
    background-size: 18px;
    margin-right: 20px;
    cursor: pointer;
}

.contact_section .right .input_wrap>ul>li>.radio_wrap li input[type="radio"]:checked~label {
    background-image: url(../img/radio_checked.svg);
}

.contact_section .right .input_wrap .checkbox_wrap input {
    display: none;
}

.contact_section .right .input_wrap .checkbox_wrap label {
    font-size: 14px;
    color: #fff;
    padding-left: 28px;
    display: flex;
    align-items: center;
    height: 18px;
    background-image: url(../img/checkbox.svg);
    background-position: left center;
    background-repeat: no-repeat;
    background-size: 18px 18px;
    cursor: pointer;
}

.contact_section .right .input_wrap .checkbox_wrap input:checked~label {
    background-image: url(../img/checkbox_checked.svg);
}

.contact_section .right .input_wrap>button {
    margin: 0 auto;
    margin-top: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 46px;
    border-radius: 46px;
    background-color: var(--mainpink);
    width: 238px;
    color: #fff;
    font-size: 18px;
    cursor: pointer;
    transition: all .3s;
}

.contact_section .right .input_wrap>button:hover {
    background-color: var(--secondpink);
}

.contact_section .right .input_wrap>button img {
    width: 18px;
    margin-right: 8px;
}



@media screen and (max-width: 1200px) {
    .contact_section {
        padding: 0;
        padding-bottom: 76px;
    }

    .contact_section .left {
        width: 500px;
        height: 597px;
    }

    .contact_section .right {
        width: calc(100% - 500px + 30px);
        /* transform: translateX(-30px); */
    }
}

@media screen and (max-width: 767px) {
    .contact_section {
        padding: 0 20px;
        height: fit-content;
        margin-top: 64px;
        padding-top: 42px;
        padding-bottom: 60px;
    }
    .contact_section .left {
        display: none;
    }
    .contact_section .right {
        width: calc(100%);
        padding: 0;
    }
}