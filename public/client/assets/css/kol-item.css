/* KOL Item Component Styles */

/* KOL Item */
.kol-item .content {
    width: 100%;
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    margin-bottom: 20px;
    height: 480px;
    transition: transform 0.3s ease;
}

.kol-item .content:hover {
    transform: translateY(-5px);
}

/* Hover Wrap */
.kol-item .hover_wrap {
    position: relative;
    overflow: hidden;
    height: 100%;
}

/* KOL Image */
.kol-item .hover_wrap img {
    width: 100%;
    height: 480px;
    object-fit: cover;
    display: block;
}

/* KOL Info Container */
.kol-item .name_btn_wrap {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 20px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7) 50%, rgba(0, 0, 0, 0.5));
    color: #fff;
    z-index: 1;
    min-height: 150px;
}

/* KOL Name */
.kol-item .name_btn_wrap .name {
    font-size: 24px;
    margin-bottom: 5px;
    color: #FF026F;
}

/* KOL Gender */
.kol-item .creator-gender {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff;
}

.kol-item .creator-gender.male {
    background-color: #3498db;
}

.kol-item .creator-gender.female {
    background-color: #e74c3c;
}

.kol-item .creator-gender.other {
    background-color: #9b59b6;
}

/* KOL Bio */
.kol-item .creator-info {
    color: #fff;
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* KOL Price */
.kol-item .creator-price {
    font-size: 18px;
    font-weight: bold;
    color: #FF026F;
    margin-bottom: 8px;
}

/* KOL Tags Container */
.kol-item .creator-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

/* KOL Tag */
.kol-item .creator-tag {
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    display: inline-block;
    margin-bottom: 5px;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .kol-item .content {
        height: 380px;
    }

    .kol-item .hover_wrap img {
        height: 380px;
    }
}

@media (max-width: 992px) {
    .kol-item .content {
        height: 350px;
    }

    .kol-item .hover_wrap img {
        height: 350px;
    }
}

@media (max-width: 576px) {
    .kol-item .content {
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
        height: 450px;
    }

    .kol-item .hover_wrap img {
        height: 450px;
    }
}
