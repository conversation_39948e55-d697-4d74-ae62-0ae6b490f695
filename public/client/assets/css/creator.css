.top_video {
    width: 100%;
    /* height: 640px; */
    height: 540px;
    position: relative;
    margin-top: 76px;
}
.top_video::after {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    content: "";
}
.top_video h2 {
    position: absolute;
    width: 100%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    text-align: center;
    z-index: 10;
    font-size: 42px;
    line-height: 1.2;
}

.top_video video {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.button_wrap {
    margin: 100px auto 0 auto;
    display: flex;
    justify-content: center;
    position: relative;
}

.button_wrap .swiper-button-next:after,
.button_wrap .swiper-button-prev:after {
    content: '';
    /* display: flex; */
    /* width: 50px; */
    display: none;
}

.button_wrap .swiper-button-next.swiper-button-disabled,
.button_wrap .swiper-button-prev.swiper-button-disabled {
    opacity: 0;
}

.button_wrap .swiper-button-prev {
    width: 32px;
    height: 32px;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.button_wrap .swiper-button-prev img {
    width: 32px;
    height: 32px;
    display: block;
}

.button_wrap .swiper-button-next {
    width: 32px;
    height: 32px;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.button_wrap .swiper-button-next img {
    width: 32px;
    height: 32px;
    display: block;
}

.button_wrap .swiper-button-prev,
.button_wrap .swiper-button-next {
    display: none;
}

.button_wrap .button_swiper .swiper-wrapper {
    display: flex;
}

.button_wrap .button_swiper .swiper-slide {
    width: auto;
}

.button_wrap .button_swiper .swiper-slide:not(:last-child) {
    margin-right: 14px;
}

.button_wrap .button_swiper .swiper-slide button {
    color: var(--mainpink);
    font-size: 20px;
    height: 40px;
    border: 1px solid var(--mainpink);
    border-radius: 40px;
    padding: 0 14px;
    cursor: pointer;
    transition: all .3s;
}

.button_wrap .button_swiper .swiper-slide button.on {
    background-color: var(--mainpink);
    color: #fff;
}

.button_wrap .button_swiper .swiper-slide button:hover {
    background-color: var(--secondpink);
    color: #fff;
}

.bottom {
    width: 100%;
    height: fit-content;
    overflow: visible;
    margin: 0 auto;
    display: block;
}

.bottom_list {
    margin: 0 auto;
    margin-top: 100px;
    width: 100%;
    max-width: 1200px;
    position: relative;
    padding: 0 15px;
}

.bottom_list .creator {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin: 0 auto;
    width: 100%;
}

.bottom_list .creator li {
    width: 100%;
    height: auto;
    position: relative;
    overflow: hidden;
    margin: 0;
    border-radius: 10px;
}

.bottom_list .creator li .hover_wrap {
    width: 100%;
    height: 100%;
}

.bottom_list .creator li .hover_wrap img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    display: block;
}

@media (max-width: 992px) {
    .bottom_list .creator li .hover_wrap img {
        height: 250px;
    }
}

@media (max-width: 576px) {
    .bottom_list .creator li .hover_wrap img {
        height: 300px;
    }
}

.bottom_list .creator li .hover_wrap::after {
    content: '';
    display: flex;
    width: 100%;
    height: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    background: rgb(0, 0, 0);
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 100%);
    transition: all .5s;
    opacity: 0;
    transition-delay: .1s;
}

.bottom_list .creator li .hover_wrap:hover::after {
    height: 100%;
    opacity: 1;
    transition-delay: 0s;
}

.bottom_list .creator li .hover_wrap>img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.bottom_list .creator li .hover_wrap .name_btn_wrap {
    position: absolute;
    bottom: 24px;
    left: 0;
    width: 100%;
    padding: 0 20px 0 24px;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.bottom_list .creator li .hover_wrap .name_btn_wrap .name {
    font-size: 20px;
    transform: translateY(350%);
    transition: .5s;
    transition-delay: 0;
    word-break:break-all;
}

.bottom_list .creator li .hover_wrap:hover .name_btn_wrap .name {
    transform: translateY(0%);
    transition-delay: 0s;
}


.bottom_list .creator li .hover_wrap .name_btn_wrap .btn_wrap {
    display: flex;
    align-items: center;
    justify-content: center;
}

.bottom_list .creator li .hover_wrap .name_btn_wrap .btn_wrap a {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    filter: drop-shadow(0px 0px 15px #58032817);
    transform: translateY(350%);
    transition: .5s;
}

.bottom_list .creator li .hover_wrap:hover .name_btn_wrap .btn_wrap a {
    transform: translateY(0%);

}

.bottom_list .creator li .hover_wrap .name_btn_wrap .btn_wrap a:first-child {
    transition-delay: 0;
}

.bottom_list .creator li .hover_wrap .name_btn_wrap .btn_wrap a:nth-child(2) {
    transition-delay: 0;
}

.bottom_list .creator li .hover_wrap .name_btn_wrap .btn_wrap a:last-child {
    transition-delay: 0;
}

.bottom_list .creator li .hover_wrap:hover .name_btn_wrap .btn_wrap a:first-child {
    transition-delay: .1s;
}

.bottom_list .creator li .hover_wrap:hover .name_btn_wrap .btn_wrap a:nth-child(2) {
    transition-delay: .2s;
}

.bottom_list .creator li .hover_wrap:hover .name_btn_wrap .btn_wrap a:last-child {
    transition-delay: .3s;
}

.bottom_list .creator li .hover_wrap .name_btn_wrap .btn_wrap a:not(:last-child) {
    margin-right: 6px;
}

.bottom_list .creator li .hover_wrap .name_btn_wrap .btn_wrap a img {
    width: 100%;
    height: 100%;
}
.bottom_inner {
    position: relative;
    padding-bottom: 100px;
}
.bottom_inner::before {
    display: none;
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 643px;
    z-index: 5;
    background-image: linear-gradient(to bottom, rgba(0,0,0,0), rgba(0,0,0,1));
    content: "";
}
.bottom_inner .livepage_btn {
    display: none;
    align-items: center;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 50%;
    bottom: 100px;
    z-index: 10;
    transform: translate(-50%, 50%);
    padding: 10px 30px;
    border-radius: 500px;
    min-width: 266px;
    height: 48px;
    background-color: var(--mainpink);
    color: #fff;
    cursor: pointer;
}
.bottom_inner .livepage_btn span {
    display: inline-block;
    font-size: 18px;
}
.bottom_inner .livepage_btn span.eng_font {
    font-size: 20px;
    margin-right: 6px;
}
.bottom_inner.live::before {
    display: block;
}
.bottom_inner.live .livepage_btn {
    display: flex;
}
@media screen and (max-width: 767px) {
    .top_video {
        height: 320px;
        margin-top: 64px;
    }

    .top_video h2 {
        font-size: 32px;
    }

    .button_wrap {
        position: relative;
        margin: 0 20px;
        margin-top: 60px;
        display: flex;
        justify-content: flex-start;
    }

    .button_wrap .swiper-button-prev::after,
    .button_wrap .swiper-button-next::after {
        content: '';
        display: flex;
        position: absolute;
        top: 0;
        right: 32px;
        height: 100%;
        z-index: 5;
        width: 130px;
        background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 90%);
        pointer-events: none;
    }
    .button_wrap .swiper-button-prev::after {
        right: auto;
        left: 32px;
        background: linear-gradient(90deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 90%);
    }
    .button_wrap .swiper-button-prev.swiper-button-disabled::after,
    .button_wrap .swiper-button-next.swiper-button-disabled::after {
        display: none;
    }
    .button_wrap .swiper-button-prev,
    .button_wrap .swiper-button-next {
        display: flex;
        z-index: 10;
        background-color: #000;
    }
    .button_wrap .swiper-button-prev img,
    .button_wrap .swiper-button-next img{
        z-index: 10;
    }
    .button_wrap .button_swiper .swiper-wrapper {
        display: flex;
        width: 588px;
    }

    .button_wrap .button_swiper {
        /* width: calc(100% - 50px); */
        margin: 0;
    }

    .button_wrap .button_swiper .swiper-slide:not(:last-child) {
        margin-right: 6px;
    }

    .button_wrap .button_swiper .swiper-slide:last-child {
        margin-right: 0;
    }

    .button_wrap .button_swiper .swiper-slide button {
        font-size: 14px;
        height: 32px;
        border-radius: 32px;
    }

    .bottom_list {
        align-items: flex-start;
        height: fit-content !important;
        margin-top: 34px;
    }

    .bottom_list .creator {
        gap: 15px;
        position: static;
        transform: translateX(0%);
        width: 100%;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        padding: 0 20px;
    }

    .bottom_list .creator li {
        width: 100%;
        height: fit-content;
        position: relative;
        overflow: hidden;
        margin: 0;
        border-radius: 10px;
    }

    /* Không cần margin-right vì đã sử dụng grid với gap */
    .bottom_list .creator li:not(:nth-child(2n)) {
        margin-right: 0;
    }

    .bottom_list .creator li .hover_wrap .name_btn_wrap {
        bottom: 15px;
        padding: 0 15px;
    }

    .bottom_list .creator li .hover_wrap .name_btn_wrap .name {
        font-size: 16px;
    }

    .bottom_list .creator li .hover_wrap .name_btn_wrap .btn_wrap a {
        width: 25px;
        height: 25px;
    }
    .bottom_inner {
        padding-bottom: 60px;
    }
    .bottom_inner::before {
        height: 140px;
        bottom: 74px;
    }
    .bottom_inner .livepage_btn {
        left: 50%;
        bottom: 76px;
        z-index: 10;
        transform: translate(-50%, 0);
        padding: 10px 30px;
        border-radius: 500px;
        min-width: 266px;
        height: 48px;
        background-color: var(--mainpink);
        color: #fff;
        cursor: pointer;
    }
    .bottom_inner .livepage_btn span {
        display: inline-block;
        font-size: 18px;
    }
    .bottom_inner .livepage_btn span.eng_font {
        font-size: 20px;
        margin-right: 6px;
    }
    .bottom_inner.live::before {
        display: block;
    }
    .bottom_inner.live .livepage_btn {
        display: flex;
    }
}
/* Responsive Grid Layout */
@media screen and (min-width: 1201px) {
    .bottom_list .creator {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }
}

@media screen and (min-width: 993px) and (max-width: 1200px) {
    .bottom_list .creator {
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
    }
}

@media screen and (min-width: 577px) and (max-width: 992px) {
    .bottom_list {
        margin-top: 70px;
    }

    .bottom_list .creator {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}

@media screen and (max-width: 576px) {
    .bottom_list {
        margin-top: 50px;
    }

    .bottom_list .creator {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .bottom_list .creator li {
        max-width: 400px;
        margin: 0 auto;
    }
}

@media screen and (max-width: 400px) {
    .bottom_list .creator {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    .bottom_list .creator li {
        width: 100%;
        max-width: 280px;
        margin: 0 auto;
    }
    .bottom_list .creator li .hover_wrap .name_btn_wrap .btn_wrap a {
        width: 36px;
        height: 36px;
    }
    .bottom_list .creator li:not(:nth-child(2n)) {
        margin-right: 0;
    }
    .bottom_list .creator li .hover_wrap::after {
        height: 100%;
        opacity: 1;
    }
    .bottom_list .creator li .hover_wrap .name_btn_wrap .name {
        transform: translateY(0%);
    }
    .bottom_list .creator li .hover_wrap .name_btn_wrap .btn_wrap a {
        transform: translateY(0%);
    }
}
