header {
    width: 100%;
    height: 76px;
    display: flex;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
}
header.fixed {
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(60px);
    -webkit-backdrop-filter: blur(60px);
}
header .hamburger {
    display: none;
    position: absolute;
    top: 0;
    right: 0;
    width: 64px;
    height: 100%;
    /* display: flex; */
    align-items: center;
    justify-content: center;
    padding: 26.75px 0;
    cursor: pointer;
}

header .hamburger .span_wrap {
    position: relative;
    width: 16.5px;
    height: 100%;
}

header .hamburger.menu_on .span_wrap {
    width: 24px;
}

header .hamburger .span_wrap span {
    position: absolute;
    left: 50%;
    width: 100%;
    height: 2px;
    border-radius: 2px;
    background-color: var(--mainpink);
    transition: all 200ms ease;
}

header .hamburger.menu_on .span_wrap span {
    background-color: #C6C7C8;
}

header .hamburger .span_wrap span:first-child {
    transform: translateX(-50%);
    top: 0;
}

header .hamburger .span_wrap span:nth-child(2) {
    transform: translateX(-50%);
    top: 50%;
}

header .hamburger .span_wrap span:last-child {
    transform: translateX(-50%);
    top: 100%;
}

header .hamburger.menu_on .span_wrap span:first-child {
    transform: translateX(-50%) rotate(45deg) translateY(-50%);
    top: 50%;
}

header .hamburger.menu_on .span_wrap span:nth-child(2) {
    transform: translateX(-50%);
    top: 50%;
    left: 0;
    opacity: 0;
}

header .hamburger.menu_on .span_wrap span:last-child {
    transform: translateX(-45%) rotate(-45deg) translateY(-30%);
    top: 50%;
}

.hamburger_menu_wrap {
    position: fixed;
    top: 0;
    left: 100%;
    width: 100%;
    height: 100%;
    background-color: #101010;
    z-index: 999;
    transition: all .2s;
    overflow: hidden;
}

.hamburger_menu_wrap.menu_on {
    left: 0;
}

.hamburger_menu_wrap .menu_wrap {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

#Accordion_wrap{
    width: 100%;
}

.que {
    width: 100%;
    position: relative;
    padding: 18px 40px;
    cursor: pointer;
    font-size: 14px;
    /* border-bottom: 1px solid #dddddd; */
    /* background-color: #fff; */
    font-size: 34px;
    color: #fff;
    opacity: 1;
    transition: all .3s;
}

/* .que.on{
    opacity: 1;
} */

.que.off{
    opacity: 0.5;
}

.anw {
    /* display: none; */
    /* overflow: hidden; */
    font-size: 14px;
    /* background-color: #f4f4f2; */
    /* padding: 27px 0; */
    /* background-color: #fff; */
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    padding: 0 40px;
    /* margin-bottom: 13px; */
    height: 0;
    overflow: hidden;
    transition: all .3s;
}

.on + .anw{
    margin-bottom: 13px;
}

.on + .anw.wrap01{
    height: 84px;
}
.on + .anw.wrap02{
    height: 336px;
}

.anw a,
.anw button {
    display: flex;
    width: 100%;
    padding: 12px 14px;
    color: #fff;
    font-size: 18px;
}

.hamburger_menu_wrap .gradient_wrap {
    position: absolute;
    top: 100%;
    border-radius: 100%;
    left: 50%;
    transform: translateX(-50%);
    filter: blur(100px);
    width: 207.69%;
    padding-top: 207.69%;
    background: rgb(255, 255, 255);
    background: linear-gradient(105deg, rgba(255, 255, 255, 1) 0%, rgba(255, 0, 110, 1) 18%, rgba(255, 0, 110, 0) 100%);
    transition: all 1s;
    transition-delay: 0;
}

.hamburger_menu_wrap.menu_on .gradient_wrap {
    top: 70%;
    transition-delay: .2s;
}

header .center_wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-width: 1140px;
    height: 100%;
    position: relative;
    padding: 0;
}

header .center_wrap h1 {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

header .center_wrap h1 a {
    width: 207px;
    height: 16px;
}

header .center_wrap h1 a img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    /* height: auto;
    height: 16px; */
}
header .center_wrap nav {
    position: relative;
}
header .center_wrap nav ul {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-60%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
}

header .center_wrap nav li:not(:last-child) {
    margin-right: 24px;
}

header .center_wrap nav li:nth-child(4) {
    width: 80px;
}
header .center_wrap nav li {
    position: relative;
}
header .center_wrap nav li > a {
    font-size: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px;
    height: 44px;
    color: #ffffff;
    opacity: 0.8;
    white-space: nowrap;
    /* transition: all .3s; */
}

header .center_wrap nav li > a:hover {
    opacity: 1;
    /* font-style: italic; */
    transform: skewX(-10deg);
}
header .center_wrap nav ul li .wrap02 {
    display: none;
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0);
    background-color: #181818;
    padding: 14px 0;
    border-radius: 12px;
}
header .center_wrap nav ul li:hover .wrap02 {
    display: block;
    width: 160px;
}
header .center_wrap nav ul li:hover .wrap02 a {
    display: block;
    font-size: 16px;
    line-height: 1.75;
    color: rgb(255, 255, 255, 0.8);
    text-align: center;
    margin-bottom: 6px;
}
header .center_wrap nav ul li:hover .wrap02 a:last-child {
    margin-bottom: 0;
}
header .center_wrap nav ul li:hover .wrap02 a:hover {
    font-weight: 600;
    color: #fff;
}

header .center_wrap .tnb {
    display: flex;
    align-items: center;
    height: 100%;
}
header .center_wrap .tnb .lang_selectbox {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 100%;
    width: 150px;
    text-align: center;
}
header .center_wrap .tnb .lang_selectbox .toggle-btn {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 700;
    color: rgb(255, 255, 255, 0.8);
    cursor: pointer;
}
header .center_wrap .tnb .lang_selectbox .toggle-btn .selected-text {
    display: block;
    margin-right: 4px;
}
header .center_wrap .tnb .lang_selectbox .toggle-btn .ico_open {
    width: 20px;
    height: 20px;
}
header .center_wrap .tnb .lang_selectbox.on .toggle-btn .ico_open img {
    transform: rotate(-180deg);
}
header .center_wrap .tnb .lang_selectbox .toggle-btn .ico_open img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: 0.5s;
}
header .center_wrap .tnb .lang_selectbox .lang_option {
    position: absolute;
    right: 0;
    top: 60px;
    background-color: #181818;
    border-radius: 12px;
    text-align: center;
    width: 160px;
    padding: 12px 0 16px 0;
    display: none;
}
header .center_wrap .tnb .lang_selectbox.on .lang_option {
    display: block;
}
header .center_wrap .tnb .lang_selectbox .lang_option li button {
    font-weight: 700;
    font-size: 16px;
    line-height: 1.75;
    color: rgb(255, 255, 255, 0.8);
    margin-bottom: 6px;
    cursor: pointer;
}
header .center_wrap .tnb .lang_selectbox .lang_option li:last-child button {
margin-bottom: 0;}
header .center_wrap .tnb .contact_btn {
    background-color: var(--mainpink);
    height: 40px;
    width: 160px;
    border-radius: 40px;
    color: #fff;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all .3s;
}
header .center_wrap .contact_btn:hover {
    background-color: var(--secondpink);
}


footer {
    background-color: #181818;
    height: 304px;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;

}

footer .top_btn {
    position: absolute;
    top: 0;
    right: 90px;
    transform: translateY(-50%);
    cursor: pointer;
    width: 54px;
    height: 54px;
    z-index: 100;
}

footer .top .logo img {
    width: 40px;
}

footer .center_wrap {
    height: 100%;
    width: 100%;
    padding: 40px 10px 32px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

footer .middle address {
    font-size: 14px;
    color: #fff;
    font-style: normal;
    line-height: 1.9;
}

footer .middle address p {
    opacity: 0.7;
}
footer .middle address p span {
    opacity: 0.3;
}

footer ul {
    display: flex;
    align-items: center;
}

footer ul li {
    display: flex;
    align-items: center;
}

footer .bottom ul li:first-child::after {
    content: '';
    display: flex;
    align-items: center;
    height: 12px;
    width: 1px;
    background-color: #fff;
    opacity: 0.3;
    margin: 0 20px;
}

footer .bottom {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
}

footer .bottom p {
    color: #fff;
    opacity: 0.3;
    font-size: 14px;
    line-height: 1.8;
}

footer .bottom ul li a {
    color: #fff;
    opacity: 0.3;
    font-size: 14px;
}

footer .bottom ul li:first-child::after {
    background-color: #D9D9D9;
    opacity: 0.22;
}


@media screen and (max-width:1200px) {
    header .center_wrap {
        padding: 0 10px;
    }
    header .center_wrap nav li:not(:last-child) {
        margin-right: 0;
    }
    footer .center_wrap {
        padding: 40px 32px 32px;
    }
}

@media screen and (max-width:980px) {
    header {
        height: 64px;
    }

    header .center_wrap {
        padding-right: 0;
    }

    header .hamburger {
        display: flex;
    }

    header .center_wrap h1 a {
        width: 190px;
        height: 14px;
    }
    /* header .center_wrap h1 a img {
        height: 14px;
    } */

    header .center_wrap nav {
        display: none;
    }

    header .center_wrap .tnb {
        display: none;
    }

    footer {
        height: 391px;
    }

    footer .top_btn {
        right: 16px;
    }


    footer .center_wrap {
        padding: 40px 20px 32px;
    }
    footer  .middle ul li:last-child p{
        width: 100%;
    }
    footer .bottom {
        font-size: 12px;
        align-items: flex-start;
        flex-direction: column;
    }

    footer .bottom ul {
        margin-top: 23px;
    }
}
