/* T<PERSON>y chỉnh Dashboard */
.fi-dashboard-page {
    padding: 1.5rem;
}

.dashboard-welcome {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.dashboard-welcome-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 0.5rem;
}

.dashboard-welcome-subtitle {
    color: #718096;
    margin-bottom: 1rem;
}

.dashboard-quick-links {
    display: grid;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    gap: 1rem;
}

@media (min-width: 640px) {
    .dashboard-quick-links {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 768px) {
    .dashboard-quick-links {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

@media (min-width: 1024px) {
    .dashboard-quick-links {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}

.dashboard-quick-link {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    padding: 1rem;
    transition: all 0.2s;
}

.dashboard-quick-link:hover {
    border-color: #3182ce;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dashboard-quick-link-title {
    display: flex;
    align-items: center;
    color: #3182ce;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.dashboard-quick-link-title svg {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.5rem;
}

.dashboard-quick-link-description {
    font-size: 0.875rem;
    color: #718096;
}
