let t=0;const e=document.querySelectorAll(".testimonial-item"),i=document.querySelectorAll(".testimonial-dot");let l=setInterval(function(){o(1)},5e3);function o(n){t+=n,t>=e.length?t=0:t<0&&(t=e.length-1),s(),a()}function s(){for(let n=0;n<e.length;n++)e[n].classList.remove("active"),i[n].classList.remove("active");console.log(e),e[t].classList.add("active"),i[t].classList.add("active")}function a(){clearInterval(l),l=setInterval(function(){o(1)},5e3)}document.addEventListener("DOMContentLoaded",function(){e.length>0&&i.length>0&&s()});
