document.addEventListener("DOMContentLoaded",function(){document.querySelectorAll(".filter-tag").forEach(t=>{t.addEventListener("click",function(){this.classList.toggle("active")})});const n=document.getElementById("filter-reset");n&&n.addEventListener("click",function(){document.querySelectorAll(".filter-input").forEach(t=>{t.value=""}),document.querySelectorAll(".filter-select").forEach(t=>{t.selectedIndex=0}),document.querySelectorAll(".filter-tag").forEach(t=>{t.classList.remove("active")})});const o=document.getElementById("filter-apply");o&&o.addEventListener("click",function(){const t=document.getElementById("filter-search").value.toLowerCase(),a=document.getElementById("filter-status").value,r=[];document.querySelectorAll(".filter-tag.active[data-tag]").forEach(e=>{r.push(e.getAttribute("data-tag"))});const i=[];document.querySelectorAll(".filter-tag.active[data-service]").forEach(e=>{i.push(e.getAttribute("data-service"))}),document.querySelectorAll("#creator-list .content").forEach(e=>{const u=e.querySelector(".name").textContent.toLowerCase(),d=e.getAttribute("data-status"),f=e.getAttribute("data-tags")?e.getAttribute("data-tags").split(","):[],g=e.getAttribute("data-services")?e.getAttribute("data-services").split(","):[];let l=!0;if(t&&!u.includes(t)&&(l=!1),a&&d!==a&&(l=!1),r.length>0){let c=!1;for(let s=0;s<r.length;s++)if(f.includes(r[s])){c=!0;break}c||(l=!1)}if(i.length>0&&l){let c=!1;for(let s=0;s<i.length;s++)if(g.includes(i[s])){c=!0;break}c||(l=!1)}l?e.style.display="":e.style.display="none"})}),document.querySelectorAll("#creator-list .content").forEach(t=>{t.addEventListener("click",function(){const a=this.getAttribute("data-id");a&&(window.location.href=`/products/${a}`)})})});
