document.addEventListener("DOMContentLoaded",function(){const n=document.querySelectorAll(".tab-item"),c=document.querySelectorAll(".process-step"),s=document.querySelector(".process-tabs");n.forEach(e=>{e.addEventListener("click",function(){const t=this.getAttribute("data-target"),o=document.getElementById(t);o&&window.scrollTo({top:o.offsetTop-s.offsetHeight,behavior:"smooth"})})}),window.addEventListener("scroll",function(){let e="";c.forEach(t=>{const o=t.offsetTop-s.offsetHeight-10,i=t.offsetHeight;window.pageYOffset>=o&&window.pageYOffset<o+i&&(e=t.getAttribute("id"))}),e&&n.forEach(t=>{t.classList.remove("active"),t.getAttribute("data-target")===e&&t.classList.add("active")})}),window.dispatchEvent(new Event("scroll"))});
