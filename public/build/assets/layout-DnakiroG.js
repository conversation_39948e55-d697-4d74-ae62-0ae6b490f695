document.addEventListener("scroll",function(){const e=document.querySelector(".header_wrap");e?window.scrollY>0?e.classList.add("fixed"):e.classList.remove("fixed"):console.warn("Element with class 'header_wrap' not found.")});document.addEventListener("click",function(e){const t=document.querySelector(".lang_selectbox");t&&!t.contains(e.target)&&t.classList.remove("on")});document.addEventListener("click",function(e){if(e.target.closest(".que")){const t=e.target.closest(".que"),o=document.querySelectorAll(".que");t.classList.contains("on")?o.forEach(s=>{s.classList.remove("on"),s.classList.remove("off")}):(o.forEach(s=>{s.classList.add("off"),s.classList.remove("on")}),t.classList.add("on"),t.classList.remove("off"))}});document.addEventListener("DOMContentLoaded",()=>{const e=document.querySelector(".skiptranslate"),t={attributes:!0,attributeFilter:["style"]},o=n=>{n.forEach(c=>{c.type==="attributes"&&c.attributeName==="style"&&(c.target.style.display==="none"?document.body.classList.add("open"):document.body.classList.remove("open"))})},s=new MutationObserver(o);e&&s.observe(e,t)});
