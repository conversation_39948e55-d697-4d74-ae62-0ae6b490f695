<?php

namespace App\Filament\Resources\KolResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class SchedulesRelationManager extends RelationManager
{
    protected static string $relationship = 'schedules';

    protected static ?string $recordTitleAttribute = 'date';

    protected static ?string $title = 'Lịch làm việc';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\DatePicker::make('date')
                    ->label('Ngày')
                    ->required(),
                Forms\Components\TimePicker::make('start_time')
                    ->label('Giờ bắt đầu')
                    ->seconds(false)
                    ->native(false)
                    ->required(),
                Forms\Components\TimePicker::make('end_time')
                    ->label('Giờ kết thúc')
                    ->seconds(false)
                    ->native(false)

                    ->required()
                    ->after('start_time'),
                Forms\Components\Toggle::make('is_available')
                    ->label('Có thể đặt lịch')
                    ->default(true),
                Forms\Components\Textarea::make('notes')
                    ->label('Ghi chú')
                    ->rows(4)
                    ->columnSpan(2),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('date')
                    ->label('Ngày')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('start_time')
                    ->label('Giờ bắt đầu')
                    ->time('H:i'),
                Tables\Columns\TextColumn::make('end_time')
                    ->label('Giờ kết thúc')
                    ->time('H:i'),
                Tables\Columns\IconColumn::make('is_available')
                    ->label('Có thể đặt lịch')
                    ->boolean(),
                Tables\Columns\TextColumn::make('notes')
                    ->label('Ghi chú')
                    ->limit(30),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('is_available')
                    ->label('Trạng thái')
                    ->options([
                        '1' => 'Có thể đặt lịch',
                        '0' => 'Không thể đặt lịch',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Thêm lịch'),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('Sửa'),
                Tables\Actions\DeleteAction::make()
                    ->label('Xóa'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Xóa đã chọn'),
                ]),
            ])
            ->defaultSort('date', 'desc');
    }
}
