<?php

namespace App\Filament\Resources\KolResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class ServicesRelationManager extends RelationManager
{
    protected static string $relationship = 'services';

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $title = 'Dịch vụ';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Tên dịch vụ')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('custom_price')
                    ->label('Giá tùy chỉnh (VNĐ)')
                    ->numeric()
                    ->helperText('Để trống nếu sử dụng giá mặc định của dịch vụ'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên dịch vụ')
                    ->searchable(),
                Tables\Columns\TextColumn::make('price')
                    ->label('Giá gốc')
                    ->money('VND'),
                Tables\Columns\TextColumn::make('custom_price')
                    ->label('Giá tùy chỉnh')
                    ->money('VND')
                    ->default('-'),
                Tables\Columns\TextColumn::make('duration')
                    ->label('Thời lượng')
                    ->formatStateUsing(fn (int $state): string => "{$state} phút"),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->label('Thêm dịch vụ')
                    ->preloadRecordSelect(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('Sửa'),
                Tables\Actions\DetachAction::make()
                    ->label('Gỡ bỏ'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make()
                        ->label('Gỡ bỏ đã chọn'),
                ]),
            ]);
    }
}
