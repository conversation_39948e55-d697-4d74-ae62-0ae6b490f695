<?php

namespace App\Filament\Resources;

use App\Filament\Resources\KolResource\Pages;
use App\Filament\Resources\KolResource\RelationManagers;
use App\Models\Kol;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Pelmered\FilamentMoneyField\Forms\Components\MoneyInput;

class KolResource extends Resource
{
    protected static ?string $model = Kol::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationLabel = 'KOL';

    protected static ?string $modelLabel = 'KOL';

    protected static ?string $pluralModelLabel = 'KOL';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                Forms\Components\Section::make('Thông tin')
                    ->schema([
                        Forms\Components\FileUpload::make('gallery')
                            ->label('<PERSON><PERSON> sưu tập ảnh')
                            ->multiple()
                            ->image()
                            ->lazy()
                            ->imagePreviewHeight('250')
                            ->panelLayout('grid')
                            ->directory('kols/gallery')
                            ->visibility('public')
                            ->reorderable(),
                        Forms\Components\TagsInput::make('categories')
                            ->label('Danh mục')
                            ->placeholder('Thêm danh mục')
                            ->suggestions([
                                'Game thủ',
                                'Streamer',
                                'Idol',
                                'Cosplayer',
                                'Người mẫu',
                                'Ca sĩ',
                                'Diễn viên',
                                'Nghệ sĩ',
                            ]),
                        Forms\Components\Repeater::make('social_links')
                            ->label('Liên kết mạng xã hội')
                            ->schema([
                                Forms\Components\Select::make('platform')
                                    ->label('Nền tảng')
                                    ->options([
                                        'facebook' => 'Facebook',
                                        'instagram' => 'Instagram',
                                        'youtube' => 'YouTube',
                                        'tiktok' => 'TikTok',
                                        'twitter' => 'Twitter',
                                        'linkedin' => 'LinkedIn',
                                        'other' => 'Khác',
                                    ])
                                    ->required(),
                                Forms\Components\TextInput::make('url')
                                    ->label('Đường dẫn')
                                    ->url()
                                    ->required(),
                            ])
                            ->columns(2)
                            ->defaultItems(0),

                        Forms\Components\Section::make('SEO Settings')
                            ->collapsed()
                            ->schema([
                                \Afatmustafa\SeoSuite\SeoSuite::make(),
                            ]),

                    ])
                    ->columnSpan(2),
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Tên KOL')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('nickname')
                            ->label('Biệt danh')
                            ->maxLength(255),
                        Forms\Components\Select::make('gender')
                            ->label('Giới tính')
                            ->options([
                                'male' => 'Nam',
                                'female' => 'Nữ',
                                'other' => 'Khác',
                            ]),
                        Forms\Components\TextInput::make('email')
                            ->label('Email')
                            ->email()
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('phone')
                            ->label('Số điện thoại')
                            ->tel()
                            ->maxLength(255),
                        MoneyInput::make('hourly_rate')
                            ->numeric()
                            ->decimals(0)
                            ->locale('vi-VN')
                            ->currency('VND')
                            ->label('Giá theo giờ (VNĐ)')
                            ->default(0)
                            ->required(),
                        Forms\Components\Toggle::make('is_active')
                            ->label('Đang hoạt động')
                            ->default(true),
                        Forms\Components\FileUpload::make('avatar')
                            ->label('Ảnh đại diện')
                            ->image()
                            ->directory('kols')
                            ->visibility('public')
                            ->imageEditor(),
                        Forms\Components\TagsInput::make('expertise')
                            ->label('Lĩnh vực chuyên môn'),
                        Forms\Components\Textarea::make('achievements')
                            ->label('Thành tích')
                            ->helperText('Mỗi dòng là một thành tích')
                            ->rows(4),
                        Forms\Components\Textarea::make('bio')
                            ->label('Tiểu sử')
                            ->rows(4),

                    ])
                    ->columnSpan(1),

            ])->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('avatar')
                    ->label('Ảnh')
                    ->circular(),
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên KOL')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('nickname')
                    ->label('Biệt danh')
                    ->searchable(),
                Tables\Columns\TextColumn::make('gender')
                    ->label('Giới tính')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'male' => 'Nam',
                        'female' => 'Nữ',
                        'other' => 'Khác',
                        default => '',
                    }),
                Tables\Columns\TextColumn::make('categories')
                    ->label('Danh mục')
                    ->badge()
                    ->separator(',')
                    ->limitList(2),
                Tables\Columns\TextColumn::make('expertise')
                    ->label('Lĩnh vực')
                    ->searchable(),

                Tables\Columns\TextColumn::make('hourly_rate')
                    ->label('Giá theo giờ')
                    ->money('VND')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Hoạt động')
                    ->boolean(),
                Tables\Columns\TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('phone')
                    ->label('Số điện thoại')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('gender')
                    ->label('Giới tính')
                    ->options([
                        'male' => 'Nam',
                        'female' => 'Nữ',
                        'other' => 'Khác',
                    ]),

                Tables\Filters\SelectFilter::make('is_active')
                    ->label('Trạng thái')
                    ->options([
                        '1' => 'Đang hoạt động',
                        '0' => 'Không hoạt động',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make()->label('Sửa'),
                Tables\Actions\ViewAction::make()->label('Xem'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()->label('Xóa đã chọn'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\SchedulesRelationManager::class,
            RelationManagers\BookingsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListKols::route('/'),
            'create' => Pages\CreateKol::route('/create'),
            'edit' => Pages\EditKol::route('/{record}/edit'),
        ];
    }
}
