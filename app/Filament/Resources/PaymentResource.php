<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PaymentResource\Pages;
use App\Models\Payment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class PaymentResource extends Resource
{
    protected static ?string $model = Payment::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationLabel = 'Thanh toán';

    protected static ?string $modelLabel = 'Thanh toán';

    protected static ?string $pluralModelLabel = 'thanh toán';

    protected static ?int $navigationSort = 5;

    // Ẩn resource khỏi navigation
    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin thanh toán')
                    ->schema([
                        Forms\Components\Select::make('booking_id')
                            ->label('Đơn đặt lịch')
                            ->relationship('booking', 'id')
                            ->searchable()
                            ->preload()
                            ->required(),
                        Forms\Components\TextInput::make('amount')
                            ->label('Số tiền (VNĐ)')
                            ->required()
                            ->numeric(),
                        Forms\Components\Select::make('payment_method')
                            ->label('Phương thức thanh toán')
                            ->options([
                                'cash' => 'Tiền mặt',
                                'bank_transfer' => 'Chuyển khoản',
                                'credit_card' => 'Thẻ tín dụng',
                                'other' => 'Khác',
                            ])
                            ->required()
                            ->default('bank_transfer')
                            ->selectablePlaceholder(false),
                        Forms\Components\Select::make('status')
                            ->label('Trạng thái')
                            ->options([
                                'pending' => 'Chờ thanh toán',
                                'completed' => 'Đã thanh toán',
                                'failed' => 'Thanh toán thất bại',
                                'refunded' => 'Đã hoàn tiền',
                            ])
                            ->required()
                            ->default('pending')
                            ->selectablePlaceholder(false),
                    ])->columns(2),

                Forms\Components\Section::make('Thông tin bổ sung')
                    ->schema([
                        Forms\Components\TextInput::make('transaction_id')
                            ->label('Mã giao dịch')
                            ->maxLength(255),
                        Forms\Components\DateTimePicker::make('payment_date')
                            ->label('Ngày thanh toán')
                            ->seconds(false),
                        Forms\Components\Textarea::make('notes')
                            ->label('Ghi chú')
                            ->rows(3),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('Mã thanh toán')
                    ->sortable(),
                Tables\Columns\TextColumn::make('booking.id')
                    ->label('Mã đơn')
                    ->sortable(),
                Tables\Columns\TextColumn::make('booking.kol.name')
                    ->label('KOL')
                    ->searchable(),
                Tables\Columns\TextColumn::make('amount')
                    ->label('Số tiền')
                    ->money('VND')
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->label('Phương thức')
                    ->formatStateUsing(function ($state) {
                        return match ($state) {
                            'cash' => 'Tiền mặt',
                            'bank_transfer' => 'Chuyển khoản',
                            'credit_card' => 'Thẻ tín dụng',
                            'other' => 'Khác',
                            default => $state,
                        };
                    }),
                Tables\Columns\TextColumn::make('status')
                    ->label('Trạng thái')
                    ->formatStateUsing(function ($state) {
                        return match ($state) {
                            'pending' => 'Chờ thanh toán',
                            'completed' => 'Đã thanh toán',
                            'failed' => 'Thanh toán thất bại',
                            'refunded' => 'Đã hoàn tiền',
                            default => $state,
                        };
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'completed' => 'success',
                        'pending' => 'warning',
                        'failed' => 'danger',
                        'refunded' => 'info',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('payment_date')
                    ->label('Ngày thanh toán')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'pending' => 'Chờ thanh toán',
                        'completed' => 'Đã thanh toán',
                        'failed' => 'Thanh toán thất bại',
                        'refunded' => 'Đã hoàn tiền',
                    ])
                    ->selectablePlaceholder(false),
                Tables\Filters\SelectFilter::make('payment_method')
                    ->label('Phương thức')
                    ->options([
                        'cash' => 'Tiền mặt',
                        'bank_transfer' => 'Chuyển khoản',
                        'credit_card' => 'Thẻ tín dụng',
                        'other' => 'Khác',
                    ])
                    ->selectablePlaceholder(false),
            ])
            ->actions([
                Tables\Actions\EditAction::make()->label('Sửa'),
                Tables\Actions\ViewAction::make()->label('Xem'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()->label('Xóa đã chọn'),
                ]),
            ])
            ->defaultSort('payment_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPayments::route('/'),
            'create' => Pages\CreatePayment::route('/create'),
            'edit' => Pages\EditPayment::route('/{record}/edit'),
        ];
    }
}
