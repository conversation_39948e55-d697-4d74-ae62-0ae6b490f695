<?php

namespace App\Filament\Resources\BookingResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class PaymentRelationManager extends RelationManager
{
    protected static string $relationship = 'payment';

    protected static ?string $recordTitleAttribute = 'transaction_id';

    protected static ?string $title = 'Thanh toán';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('amount')
                    ->label('Số tiền (VNĐ)')
                    ->required()
                    ->numeric(),
                Forms\Components\Select::make('payment_method')
                    ->label('Phương thức thanh toán')
                    ->options([
                        'cash' => 'Tiền mặt',
                        'bank_transfer' => 'Chuyển khoản',
                        'credit_card' => 'Thẻ tín dụng',
                        'other' => 'Khác',
                    ])
                    ->required()
                    ->default('bank_transfer')
                    ->selectablePlaceholder(false),
                Forms\Components\Select::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'pending' => 'Chờ thanh toán',
                        'completed' => 'Đã thanh toán',
                        'failed' => 'Thanh toán thất bại',
                        'refunded' => 'Đã hoàn tiền',
                    ])
                    ->required()
                    ->default('pending')
                    ->selectablePlaceholder(false),
                Forms\Components\TextInput::make('transaction_id')
                    ->label('Mã giao dịch')
                    ->maxLength(255),
                Forms\Components\DateTimePicker::make('payment_date')
                    ->label('Ngày thanh toán'),
                Forms\Components\Textarea::make('notes')
                    ->label('Ghi chú')
                    ->rows(3),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('Mã thanh toán')
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->label('Số tiền')
                    ->money('VND')
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->label('Phương thức')
                    ->formatStateUsing(function ($state) {
                        return match ($state) {
                            'cash' => 'Tiền mặt',
                            'bank_transfer' => 'Chuyển khoản',
                            'credit_card' => 'Thẻ tín dụng',
                            'other' => 'Khác',
                            default => $state,
                        };
                    }),
                Tables\Columns\TextColumn::make('status')
                    ->label('Trạng thái')
                    ->formatStateUsing(function ($state) {
                        return match ($state) {
                            'pending' => 'Chờ thanh toán',
                            'completed' => 'Đã thanh toán',
                            'failed' => 'Thanh toán thất bại',
                            'refunded' => 'Đã hoàn tiền',
                            default => $state,
                        };
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'completed' => 'success',
                        'pending' => 'warning',
                        'failed' => 'danger',
                        'refunded' => 'info',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('payment_date')
                    ->label('Ngày thanh toán')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'pending' => 'Chờ thanh toán',
                        'completed' => 'Đã thanh toán',
                        'failed' => 'Thanh toán thất bại',
                        'refunded' => 'Đã hoàn tiền',
                    ])
                    ->selectablePlaceholder(false),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Thêm thanh toán'),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('Sửa'),
                Tables\Actions\DeleteAction::make()
                    ->label('Xóa'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Xóa đã chọn'),
                ]),
            ]);
    }
}
