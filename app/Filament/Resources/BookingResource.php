<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BookingResource\Pages;
use App\Filament\Resources\BookingResource\RelationManagers;
use App\Models\Booking;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class BookingResource extends Resource
{
    protected static ?string $model = Booking::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar';

    protected static ?string $navigationLabel = 'Đặt lịch';

    protected static ?string $modelLabel = 'Đơn đặt lịch';

    protected static ?string $pluralModelLabel = 'đơn đặt lịch';

    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin đặt lịch')
                    ->schema([
                        Forms\Components\TextInput::make('code')
                            ->label('Mã đặt lịch')
                            ->disabled()
                            ->dehydrated(),

                        Forms\Components\Select::make('kol_id')
                            ->label('KOL')
                            ->relationship('kol', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\Select::make('customer_id')
                            ->label('Khách hàng')
                            ->relationship('customer', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),
                        Forms\Components\Select::make('status')
                            ->label('Trạng thái')
                            ->options([
                                'pending' => 'Chờ xác nhận',
                                'confirmed' => 'Đã xác nhận',
                                'completed' => 'Đã hoàn thành',
                                'cancelled' => 'Đã hủy',
                            ])
                            ->required()
                            ->default('pending')
                            ->selectablePlaceholder(false),
                    ])->columns(2),

                Forms\Components\Section::make('Thời gian và giá cả')
                    ->schema([
                        Forms\Components\DateTimePicker::make('start_time')
                            ->label('Thời gian bắt đầu')
                            ->seconds(false)
                            ->required(),
                        Forms\Components\DateTimePicker::make('end_time')
                            ->label('Thời gian kết thúc')
                            ->seconds(false)
                            ->required()
                            ->after('start_time'),
                        Forms\Components\TextInput::make('total_price')
                            ->label('Tổng tiền (VNĐ)')
                            ->required()
                            ->numeric(),
                        Forms\Components\Textarea::make('notes')
                            ->label('Ghi chú')
                            ->rows(3),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->label('Mã đặt lịch')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('kol.name')
                    ->label('KOL')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('customer.name')
                    ->label('Khách hàng')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('start_time')
                    ->label('Bắt đầu')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_price')
                    ->label('Tổng tiền')
                    ->money('VND')
                    ->sortable(),
                Tables\Columns\SelectColumn::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'pending' => 'Chờ xác nhận',
                        'confirmed' => 'Đã xác nhận',
                        'completed' => 'Đã hoàn thành',
                        'cancelled' => 'Đã hủy',
                    ])
                    ->selectablePlaceholder(false)
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'pending' => 'Chờ xác nhận',
                        'confirmed' => 'Đã xác nhận',
                        'completed' => 'Đã hoàn thành',
                        'cancelled' => 'Đã hủy',
                    ])
                    ->selectablePlaceholder(false),
                Tables\Filters\SelectFilter::make('kol_id')
                    ->label('KOL')
                    ->relationship('kol', 'name'),

            ])
            ->actions([
                Tables\Actions\EditAction::make()->label('Sửa'),
                Tables\Actions\ViewAction::make()->label('Xem'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()->label('Xóa đã chọn'),
                ]),
            ])
            ->defaultSort('start_time', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\PaymentRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBookings::route('/'),
            'create' => Pages\CreateBooking::route('/create'),
            'edit' => Pages\EditBooking::route('/{record}/edit'),
        ];
    }
}
