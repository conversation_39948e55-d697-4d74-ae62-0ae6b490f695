<?php

namespace App\Filament\Resources\ServiceResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class KolsRelationManager extends RelationManager
{
    protected static string $relationship = 'kols';

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $title = 'KOL cung cấp dịch vụ';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Tên KOL')
                    ->required()
                    ->maxLength(255)
                    ->disabled(),
                Forms\Components\TextInput::make('custom_price')
                    ->label('Giá tùy chỉnh (VNĐ)')
                    ->numeric()
                    ->helperText('Để trống nếu sử dụng giá mặc định của dịch vụ'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('avatar')
                    ->label('Ảnh')
                    ->circular(),
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên KOL')
                    ->searchable(),
                Tables\Columns\TextColumn::make('expertise')
                    ->label('Lĩnh vực')
                    ->searchable(),
                Tables\Columns\TextColumn::make('hourly_rate')
                    ->label('Giá theo giờ')
                    ->money('VND'),
                Tables\Columns\TextColumn::make('custom_price')
                    ->label('Giá tùy chỉnh')
                    ->money('VND')
                    ->default('-'),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Hoạt động')
                    ->boolean(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->label('Thêm KOL')
                    ->preloadRecordSelect(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('Sửa'),
                Tables\Actions\DetachAction::make()
                    ->label('Gỡ bỏ'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DetachBulkAction::make()
                        ->label('Gỡ bỏ đã chọn'),
                ]),
            ]);
    }
}
