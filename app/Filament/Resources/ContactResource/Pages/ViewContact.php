<?php

namespace App\Filament\Resources\ContactResource\Pages;

use App\Filament\Resources\ContactResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;

class ViewContact extends ViewRecord
{
    protected static string $resource = ContactResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Thông tin liên hệ')
                    ->schema([
                        TextEntry::make('type')
                            ->label('Loại')
                            ->formatStateUsing(fn(int $state): string => match ($state) {
                                1 => 'Brand',
                                2 => 'Creator',
                                default => '',
                            }),
                        TextEntry::make('created_at')
                            ->label('Ngày gửi')
                            ->dateTime('d/m/Y H:i'),

                        // Brand fields
                        TextEntry::make('data.name')
                            ->label('Tên')
                            ->visible(fn ($record) => $record->type === 1),
                        TextEntry::make('data.email')
                            ->label('Email')
                            ->visible(fn ($record) => $record->type === 1),
                        TextEntry::make('data.phone')
                            ->label('Số điện thoại')
                            ->visible(fn ($record) => $record->type === 1),
                        TextEntry::make('data.company')
                            ->label('Công ty')
                            ->visible(fn ($record) => $record->type === 1),
                        TextEntry::make('data.help')
                            ->label('Yêu cầu hỗ trợ')
                            ->visible(fn ($record) => $record->type === 1),
                        TextEntry::make('data.hear')
                            ->label('Biết đến từ')
                            ->visible(fn ($record) => $record->type === 1),

                        // Creator fields
                        TextEntry::make('data.name')
                            ->label('Tên')
                            ->visible(fn ($record) => $record->type === 2),
                        TextEntry::make('data.email')
                            ->label('Email')
                            ->visible(fn ($record) => $record->type === 2),
                        TextEntry::make('data.phone')
                            ->label('Số điện thoại')
                            ->visible(fn ($record) => $record->type === 2),
                        TextEntry::make('data.birth')
                            ->label('Ngày sinh')
                            ->visible(fn ($record) => $record->type === 2),
                        TextEntry::make('data.gender')
                            ->label('Giới tính')
                            ->visible(fn ($record) => $record->type === 2),
                        TextEntry::make('data.link')
                            ->label('Liên kết mạng xã hội')
                            ->visible(fn ($record) => $record->type === 2),
                        TextEntry::make('data.reason')
                            ->label('Lý do nộp đơn')
                            ->visible(fn ($record) => $record->type === 2),
                        TextEntry::make('data.hear')
                            ->label('Biết đến từ')
                            ->visible(fn ($record) => $record->type === 2),
                    ])->columns(2)
            ]);
    }
}
