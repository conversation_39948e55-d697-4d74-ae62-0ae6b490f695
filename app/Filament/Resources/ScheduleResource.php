<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ScheduleResource\Pages;
use App\Models\Schedule;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ScheduleResource extends Resource
{
    protected static ?string $model = Schedule::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';

    protected static ?string $navigationLabel = 'Lịch làm việc';

    protected static ?string $modelLabel = 'Lịch làm việc';

    protected static ?string $pluralModelLabel = 'lịch làm việc';

    protected static ?int $navigationSort = 6;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin lịch làm việc')
                    ->schema([
                        Forms\Components\Select::make('kol_id')
                            ->label('KOL')
                            ->relationship('kol', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),
                        Forms\Components\DatePicker::make('date')
                            ->label('Ngày')
                            ->required(),
                        Forms\Components\TimePicker::make('start_time')
                            ->label('Giờ bắt đầu')
                            ->seconds(false)
                            ->required(),
                        Forms\Components\TimePicker::make('end_time')
                            ->label('Giờ kết thúc')
                            ->seconds(false)
                            ->required()
                            ->after('start_time'),
                        Forms\Components\Toggle::make('is_available')
                            ->label('Có thể đặt lịch')
                            ->default(true),
                        Forms\Components\Textarea::make('notes')
                            ->label('Ghi chú')
                            ->rows(3),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('kol.name')
                    ->label('KOL')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('date')
                    ->label('Ngày')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('start_time')
                    ->label('Giờ bắt đầu')
                    ->time('H:i'),
                Tables\Columns\TextColumn::make('end_time')
                    ->label('Giờ kết thúc')
                    ->time('H:i'),
                Tables\Columns\IconColumn::make('is_available')
                    ->label('Có thể đặt lịch')
                    ->boolean(),
                Tables\Columns\TextColumn::make('notes')
                    ->label('Ghi chú')
                    ->limit(30)
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('kol_id')
                    ->label('KOL')
                    ->relationship('kol', 'name'),
                Tables\Filters\SelectFilter::make('is_available')
                    ->label('Trạng thái')
                    ->options([
                        '1' => 'Có thể đặt lịch',
                        '0' => 'Không thể đặt lịch',
                    ]),
                Tables\Filters\Filter::make('date')
                    ->form([
                        Forms\Components\DatePicker::make('from_date')
                            ->label('Từ ngày'),
                        Forms\Components\DatePicker::make('until_date')
                            ->label('Đến ngày'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from_date'],
                                fn (Builder $query, $date): Builder => $query->whereDate('date', '>=', $date),
                            )
                            ->when(
                                $data['until_date'],
                                fn (Builder $query, $date): Builder => $query->whereDate('date', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()->label('Sửa'),
                Tables\Actions\DeleteAction::make()->label('Xóa'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()->label('Xóa đã chọn'),
                ]),
            ])
            ->defaultSort('date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSchedules::route('/'),
            'create' => Pages\CreateSchedule::route('/create'),
            'edit' => Pages\EditSchedule::route('/{record}/edit'),
        ];
    }
}
