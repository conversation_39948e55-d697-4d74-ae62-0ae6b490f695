<?php

namespace App\Filament\Widgets;

use App\Models\Schedule;
use Carbon\Carbon;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class UpcomingSchedules extends BaseWidget
{
    protected static ?int $sort = 5;

    protected int|string|array $columnSpan = 'full';

    protected static ?string $heading = 'Lịch làm việc sắp tới';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Schedule::query()
                    ->where('date', '>=', Carbon::today())
                    ->where('is_available', true)
                    ->orderBy('date')
                    ->orderBy('start_time')
                    ->limit(5)
            )
            ->columns([
                Tables\Columns\TextColumn::make('kol.name')
                    ->label('KOL')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('date')
                    ->label('Ngày')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('start_time')
                    ->label('Giờ bắt đầu')
                    ->time('H:i'),
                Tables\Columns\TextColumn::make('end_time')
                    ->label('Giờ kết thúc')
                    ->time('H:i'),
                Tables\Columns\IconColumn::make('is_available')
                    ->label('Có thể đặt lịch')
                    ->boolean(),
                Tables\Columns\TextColumn::make('notes')
                    ->label('Ghi chú')
                    ->limit(30),
            ]);
    }
}
