<?php

namespace App\Filament\Widgets;

use App\Models\Booking;
use App\Models\Customer;
use App\Models\Kol;
use App\Models\Payment;
use App\Models\Service;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends BaseWidget
{
    protected static ?int $sort = 1;

    protected function getStats(): array
    {
        $totalRevenue = Payment::where('status', 'completed')->sum('amount');
        $pendingRevenue = Payment::where('status', 'pending')->sum('amount');
        $completedBookings = Booking::where('status', 'completed')->count();
        $pendingBookings = Booking::where('status', 'pending')->count();
        $confirmedBookings = Booking::where('status', 'confirmed')->count();
        $totalKols = Kol::count();
        $totalCustomers = Customer::count();
        $totalServices = Service::count();

        $topKols = Kol::withCount('bookings')
            ->orderBy('bookings_count', 'desc')
            ->limit(1)
            ->first();

        $topServices = Service::withCount('bookings')
            ->orderBy('bookings_count', 'desc')
            ->limit(1)
            ->first();

        return [
            Stat::make('Tổng doanh thu', number_format($totalRevenue, 0, ',', '.').' VNĐ')
                ->description('Từ các thanh toán đã hoàn thành')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color('success')
                ->chart([
                    $totalRevenue * 0.7, $totalRevenue * 0.8, $totalRevenue * 0.9, $totalRevenue,
                ]),

            Stat::make('Doanh thu chờ xử lý', number_format($pendingRevenue, 0, ',', '.').' VNĐ')
                ->description('Từ các thanh toán đang chờ')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            Stat::make('Tổng đơn đặt lịch', $completedBookings + $pendingBookings + $confirmedBookings)
                ->description($completedBookings.' hoàn thành, '.$confirmedBookings.' xác nhận, '.$pendingBookings.' chờ')
                ->descriptionIcon('heroicon-m-calendar')
                ->color('primary'),

            Stat::make('Tổng KOL', $totalKols)
                ->description($topKols ? 'KOL nổi bật: '.$topKols->name : '')
                ->descriptionIcon('heroicon-m-user-group')
                ->color('success'),

            Stat::make('Tổng dịch vụ', $totalServices)
                ->description($topServices ? 'Dịch vụ phổ biến: '.$topServices->name : '')
                ->descriptionIcon('heroicon-m-video-camera')
                ->color('info'),

            Stat::make('Tổng khách hàng', $totalCustomers)
                ->description('Khách hàng đã đăng ký')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary'),
        ];
    }
}
