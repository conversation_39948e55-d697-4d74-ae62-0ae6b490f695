<?php

namespace App\Filament\Widgets;

use App\Models\Payment;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;

class RevenueChart extends ChartWidget
{
    protected static ?string $heading = 'Biểu đồ doanh thu';

    protected static ?int $sort = 2;

    protected int|string|array $columnSpan = 'full';

    public ?string $filter = 'month';

    protected function getFilters(): ?array
    {
        return [
            'month' => 'Tháng này',
            'last_month' => 'Tháng trước',
            'year' => 'Năm nay',
            'all' => 'Tất cả',
        ];
    }

    protected function getData(): array
    {
        $activeFilter = $this->filter;

        $payments = Payment::query()
            ->where('status', 'completed')
            ->when($activeFilter === 'month', function ($query) {
                return $query->whereMonth('payment_date', Carbon::now()->month)
                    ->whereYear('payment_date', Carbon::now()->year);
            })
            ->when($activeFilter === 'last_month', function ($query) {
                return $query->whereMonth('payment_date', Carbon::now()->subMonth()->month)
                    ->whereYear('payment_date', Carbon::now()->subMonth()->year);
            })
            ->when($activeFilter === 'year', function ($query) {
                return $query->whereYear('payment_date', Carbon::now()->year);
            })
            ->get();

        $groupedData = [];

        if ($activeFilter === 'month' || $activeFilter === 'last_month') {
            // Group by day
            foreach ($payments as $payment) {
                $date = Carbon::parse($payment->payment_date)->format('d/m');
                if (! isset($groupedData[$date])) {
                    $groupedData[$date] = 0;
                }
                $groupedData[$date] += $payment->amount;
            }
        } elseif ($activeFilter === 'year') {
            // Group by month
            foreach ($payments as $payment) {
                $month = Carbon::parse($payment->payment_date)->format('m/Y');
                if (! isset($groupedData[$month])) {
                    $groupedData[$month] = 0;
                }
                $groupedData[$month] += $payment->amount;
            }
        } else {
            // Group by year
            foreach ($payments as $payment) {
                $year = Carbon::parse($payment->payment_date)->format('Y');
                if (! isset($groupedData[$year])) {
                    $groupedData[$year] = 0;
                }
                $groupedData[$year] += $payment->amount;
            }
        }

        // Sort by key
        ksort($groupedData);

        return [
            'datasets' => [
                [
                    'label' => 'Doanh thu (VNĐ)',
                    'data' => array_values($groupedData),
                    'backgroundColor' => '#36A2EB',
                    'borderColor' => '#36A2EB',
                ],
            ],
            'labels' => array_keys($groupedData),
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}
