<?php

namespace App\Filament\Widgets;

use App\Models\Booking;
use Filament\Widgets\ChartWidget;

class BookingStatusChart extends ChartWidget
{
    protected static ?string $heading = 'Trạng thái đơn đặt lịch';

    protected static ?int $sort = 3;

    protected function getData(): array
    {
        $statuses = [
            'pending' => 'Chờ xác nhận',
            'confirmed' => 'Đã xác nhận',
            'completed' => 'Đã hoàn thành',
            'cancelled' => 'Đã hủy',
        ];

        $bookingsByStatus = [];
        $colors = [
            'pending' => '#F59E0B',
            'confirmed' => '#3B82F6',
            'completed' => '#10B981',
            'cancelled' => '#EF4444',
        ];

        foreach ($statuses as $status => $label) {
            $bookingsByStatus[$status] = Booking::where('status', $status)->count();
        }

        return [
            'datasets' => [
                [
                    'label' => 'Số lượng đơn',
                    'data' => array_values($bookingsByStatus),
                    'backgroundColor' => array_values(array_intersect_key($colors, $bookingsByStatus)),
                ],
            ],
            'labels' => array_values(array_intersect_key($statuses, $bookingsByStatus)),
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }
}
