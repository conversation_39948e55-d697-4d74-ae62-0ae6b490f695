<?php

namespace App\Filament\Pages;

use App\Models\Setting;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;

class Settings extends Page implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];

    protected static ?string $navigationIcon = 'heroicon-s-cog';

    protected static string $view = 'filament.pages.settings';

    protected static ?string $navigationLabel = 'Cài đặt';

    protected static ?int $navigationSort = 9;

    public function mount(): void
    {
        $data = Setting::all()->pluck('value', 'key')->toArray();
        foreach ($data as $key => &$value) {
            if (json_validate($value)) {
                $value = json_decode($value, true);
            }
        }
        $this->form->fill($data);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('Tabs')
                    ->tabs([
                        Tabs\Tab::make('Chung')
                            ->schema([
                                FileUpload::make('logo')
                                    ->lazy()
                                    ->image()
                                    ->imagePreviewHeight('250')
                                    ->visibility('public')
                                    ->directory('images')
                                    ->columnSpan(1),
                                FileUpload::make('fav_icon')
                                    ->lazy()
                                    ->image()
                                    ->visibility('public')
                                    ->directory('images')
                                    ->columnSpan(1),

                                TextInput::make('site_name')
                                    ->label('Tên website'),
                                TextInput::make('site_title')
                                    ->label('Tiêu đề website'),
                                Textarea::make('site_desc')
                                    ->label('Mô tả trang web')
                                    ->columnSpan(2),
                            ])->columns(2),
                        Tabs\Tab::make('Thanh toán')
                            ->schema([
                                TextInput::make('bank_name')
                                    ->label("Tên ngân hàng")
                                    ->columnSpan(2),
                                TextInput::make('bank_account')
                                    ->label("Số tài khoản")
                                    ->columnSpan(2),
                                TextInput::make('bank_account_name')
                                    ->label("Tên tài khoản")
                                    ->columnSpan(2),
                                TextInput::make('bank_code')
                                    ->label("Mã Bank")
                                    ->helperText("Xem tại https://www.vietqr.io/")
                                    ->columnSpan(2),
                            ]),
                        Tabs\Tab::make('Trang chủ')
                            ->schema([
                                Section::make('Tiêu đề')
                                    ->collapsible()
                                    ->schema([
                                        TextInput::make('title_1')->label('Tiêu đề 1'),
                                        TextInput::make('title_2')->label('Tiêu đề 2'),
                                        FileUpload::make('video_home')
                                            ->downloadable()
                                            ->label('Video'),
                                    ])->columns(3),
                                Section::make('Về chúng tôi')
                                    ->collapsed()
                                    ->schema([
                                        Repeater::make('home_about_us')
                                            ->schema([
                                                TextInput::make('name')
                                                    ->columnSpan(1)
                                                    ->label('Tên')
                                                    ->required(),
                                                TextInput::make('unit')
                                                    ->columnSpan(1)
                                                    ->label('Đơn vị')
                                                    ->required(),
                                                TextInput::make('value')
                                                    ->columnSpan(1)
                                                    ->label('Giá trị')
                                                    ->required(),
                                            ])
                                            ->reorderableWithButtons()
                                            ->columns(3),
                                    ]),
                            ]),
                        Tabs\Tab::make('Chân trang')
                            ->schema([
                                TextInput::make('company_name')->label('Tên công ty'),
                                TextInput::make('business_reg_num')->label('Số đăng ký kinh doanh'),
                                TextInput::make('person_protecting')->label('Người chịu trách nhiệm bảo vệ thông tin cá nhân'),
                                TextInput::make('footer_contact')->label('Liên hệ'),
                                TextInput::make('copyright')->label('Copyright')->columnSpan(4),
                                Select::make('company_introduction')
                                    ->native(false)
                                    ->label('Giới thiệu công ty'),
                                Select::make('privacy_policy')
                                    ->native(false)
                                    ->label('Chính sách bảo mật'),
                            ])->columns(4),

                    ]),

            ])->statePath('data');
    }

    public function submit()
    {
        $data = $this->form->getState();
        foreach ($data as $key => $value) {
            Setting::updateOrCreate(
                ['key' => $key],
                ['value' => is_array($value) ? json_encode($value) : $value]
            );
        }
        // clear cache
        \Cache::clear();
        Notification::make()
            ->title('Lưu thành công')
            ->success()
            ->send();
    }
}
