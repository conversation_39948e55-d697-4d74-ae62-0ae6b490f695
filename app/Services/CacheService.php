<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class CacheService
{
    const CONFIG_FE = 'config_fe';

    const CACHE_CONFIG_KEY = 'fe_cache';

    const CACHE_CONFIG_TIME_KEY = 'fe_cache_time';

    public function __construct()
    {
        // Khi khởi tạo service, tự load config_fe nếu chưa có
        if (! Cache::has(self::CONFIG_FE)) {
            $this->loadConfig();
        }
    }

    /**
     * @param  int  $ttl
     * @return mixed
     */
    public function remember(string $key, \Closure $callback, $ttl = null)
    {
        if (! get_config(self::CACHE_CONFIG_KEY)) {
            return $callback();
        }
        if (is_null($ttl)) {
            $ttl = get_config(self::CACHE_CONFIG_TIME_KEY) ?? 604800;
        }

        return Cache::remember($key, $ttl, $callback);
    }

    public function loadConfig()
    {
        return Cache::remember(self::CONFIG_FE, 604800, function () {
            return Setting::select(['key', 'value'])->get()->keyBy('key')->toArray();
        });
    }

    public function getConfig($key, $default = null)
    {
        $data = Cache::get(CacheService::CONFIG_FE, []);
        if (isset($data[$key]['value'])) {
            return $data[$key]['value'];
        }

        return Setting::where('key', $key)->value('value') ?? $default;
    }
}
