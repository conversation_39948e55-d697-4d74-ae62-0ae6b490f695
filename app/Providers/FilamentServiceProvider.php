<?php

namespace App\Providers;

use Filament\Support\Assets\Css;
use Filament\Support\Facades\FilamentAsset;
use Illuminate\Support\ServiceProvider;

class FilamentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Đăng ký CSS tùy chỉnh cho admin panel
        FilamentAsset::register([
            Css::make('admin-theme', asset('css/filament/admin/theme.css')),
            Css::make('dashboard', asset('css/filament/admin/dashboard.css')),
        ]);
    }
}
