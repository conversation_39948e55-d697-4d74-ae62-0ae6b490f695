<?php

namespace App\Livewire;

use App\Models\Booking;
use App\Models\Kol;
use App\Models\Schedule;
use Carbon\Carbon;
use Livewire\Component;

class ProductDetail extends Component
{
    public $productId;

    public $currentHeaderSlide = 0;

    public $showBookingModal = false;

    public $selectedDate = null;

    public $selectedTime = null;

    public $selectedStartTime = null;

    public $selectedEndTime = null;

    public $selectedHours = 0;

    public $availableStartTimes = [];

    public $availableEndTimes = [];

    public $workingSchedules = [];

    public $services = [];

    public $kol = null;

    public $schedules = [];

    public $isLoading = false;

    public $autoSlideInterval = 5000; // 5 giây cho mỗi slide

    // Biến cho lịch
    public $currentMonth;

    public $currentYear;

    public $daysInMonth = [];

    public $firstDayOfMonth;

    public $availableTimeSlots = [];

    // Mount method runs when component is initialized
    public function mount($productId = null)
    {
        $this->productId = $productId;

        // Khởi tạo lịch với tháng hiện tại
        $today = Carbon::today();
        $this->currentMonth = $today->month;
        $this->currentYear = $today->year;
        $this->generateCalendar();

        try {
            // Lấy dữ liệu KOL từ cơ sở dữ liệu
            $this->kol = Kol::findOrFail($productId);

            // Lấy lịch làm việc từ bảng schedules
            $schedules = Schedule::where('kol_id', $productId)
                ->where('date', '>=', Carbon::today())
                ->orderBy('date')
                ->orderBy('start_time')
                ->get();

            // Nhóm lịch làm việc theo ngày trong tuần
            $groupedSchedules = [];
            foreach ($schedules as $schedule) {
                $date = Carbon::parse($schedule->date);
                $dayName = $date->translatedFormat('l'); // Tên thứ trong tuần

                $startTime = Carbon::parse($schedule->start_time)->format('H:i');
                $endTime = Carbon::parse($schedule->end_time)->format('H:i');
                $timeRange = $startTime.' - '.$endTime;

                if (! isset($groupedSchedules[$dayName])) {
                    $groupedSchedules[$dayName] = [
                        'day' => $dayName,
                        'hours' => $timeRange,
                    ];
                } else {
                    $groupedSchedules[$dayName]['hours'] .= ', '.$timeRange;
                }
            }

            $this->schedules = array_values($groupedSchedules);

            // Không còn sử dụng dịch vụ
            $this->services = [];

        } catch (\Exception $e) {
            // Nếu không tìm thấy KOL, sử dụng dữ liệu mẫu
            $this->kol = null;
            $this->schedules = [];
            $this->services = [];

        }
    }

    // Methods for slideshow
    public function changeHeaderSlide($direction)
    {
        // Sử dụng ảnh đại diện và gallery của KOL
        $totalSlides = $this->kol && $this->kol->gallery ? count($this->kol->gallery) + 1 : 3; // +1 cho ảnh đại diện
        $this->currentHeaderSlide = ($this->currentHeaderSlide + $direction) % $totalSlides;
        if ($this->currentHeaderSlide < 0) {
            $this->currentHeaderSlide = $totalSlides - 1;
        }
    }

    public function setHeaderSlide($index)
    {
        $this->currentHeaderSlide = $index;
    }

    public function autoAdvanceSlide()
    {
        $this->changeHeaderSlide(1);
    }

    // Methods for booking modal
    public function openBookingModal()
    {
        $this->showBookingModal = true;
    }

    public function closeBookingModal()
    {
        $this->showBookingModal = false;
    }

    // Tạo lịch cho tháng hiện tại
    public function generateCalendar()
    {
        $firstDay = Carbon::createFromDate($this->currentYear, $this->currentMonth, 1);
        $lastDay = Carbon::createFromDate($this->currentYear, $this->currentMonth, 1)->endOfMonth();

        $this->firstDayOfMonth = $firstDay->dayOfWeek; // 0 (Chủ nhật) đến 6 (Thứ bảy)

        // Lấy tất cả lịch hoạt động của KOL trong tháng này
        $availableDates = [];
        if ($this->productId) {
            $schedules = Schedule::where('kol_id', $this->productId)
                ->whereYear('date', $this->currentYear)
                ->whereMonth('date', $this->currentMonth)
                ->get();

            foreach ($schedules as $schedule) {
                $scheduleDate = Carbon::parse($schedule->date)->format('Y-m-d');
                $availableDates[$scheduleDate] = true;
            }
        }

        // Tạo mảng các ngày trong tháng
        $this->daysInMonth = [];
        for ($day = 1; $day <= $lastDay->day; $day++) {
            $date = Carbon::createFromDate($this->currentYear, $this->currentMonth, $day);
            $dateStr = $date->format('Y-m-d');

            // Ngày có sẵn nếu nó không phải là ngày trong quá khứ và có lịch hoạt động
            $isAvailable = $date->greaterThanOrEqualTo(Carbon::today()) && isset($availableDates[$dateStr]);

            $this->daysInMonth[] = [
                'day' => $day,
                'date' => $dateStr,
                'isToday' => $date->isToday(),
                'isPast' => $date->isPast(),
                'isAvailable' => $isAvailable,
            ];
        }
    }

    // Chuyển sang tháng trước
    public function prevMonth()
    {
        $date = Carbon::createFromDate($this->currentYear, $this->currentMonth, 1)->subMonth();
        $this->currentMonth = $date->month;
        $this->currentYear = $date->year;
        $this->generateCalendar();
    }

    // Chuyển sang tháng sau
    public function nextMonth()
    {
        $date = Carbon::createFromDate($this->currentYear, $this->currentMonth, 1)->addMonth();
        $this->currentMonth = $date->month;
        $this->currentYear = $date->year;
        $this->generateCalendar();
    }

    // Lấy tên tháng hiện tại
    public function getCurrentMonthName()
    {
        return Carbon::createFromDate($this->currentYear, $this->currentMonth, 1)->translatedFormat('F Y');
    }

    // Chọn ngày
    public function selectDate($date)
    {
        $this->selectedDate = $date;
        $this->loadAvailableTimeSlots($date);
    }

    // Lấy các khung giờ có sẵn cho ngày đã chọn
    public function loadAvailableTimeSlots($day)
    {
        $selectedDate = Carbon::createFromDate($this->currentYear, $this->currentMonth, $day);
        $dateStr = $selectedDate->format('Y-m-d');

        // Lấy lịch làm việc của KOL cho ngày đã chọn
        $schedules = Schedule::where('kol_id', $this->productId)
            ->whereDate('date', $dateStr)
            ->where('is_available', true)
            ->orderBy('start_time')
            ->get();

        $this->workingSchedules = [];
        $this->availableStartTimes = [];
        $this->availableEndTimes = [];
        $bookedTimeSlots = [];

        // Lấy các đơn đặt lịch đã xác nhận cho ngày này
        $confirmedBookings = \App\Models\Booking::where('kol_id', $this->productId)
            ->where('status', 'confirmed')
            ->whereDate('start_time', $dateStr)
            ->get();

        // Tạo danh sách các giờ đã đặt
        foreach ($confirmedBookings as $booking) {
            $startTime = Carbon::parse($booking->start_time);
            $endTime = Carbon::parse($booking->end_time);

            // Đánh dấu tất cả các giờ trong khoảng thời gian đã đặt
            $current = clone $startTime;
            while ($current->lt($endTime)) {
                $bookedTimeSlots[$current->format('H:i')] = true;
                $current->addHour();
            }
        }

        // Xử lý các khung giờ làm việc của KOL
        foreach ($schedules as $schedule) {
            $startTime = Carbon::parse($schedule->start_time);
            $endTime = Carbon::parse($schedule->end_time);

            $this->workingSchedules[] = [
                'start_time' => $startTime->format('H:i'),
                'end_time' => $endTime->format('H:i'),
                'start_hour' => $startTime->hour,
                'end_hour' => $endTime->hour,
            ];
        }

        // Nếu không có lịch làm việc, sử dụng lịch mặc định
        if (empty($this->workingSchedules)) {
            $this->workingSchedules = [
                [
                    'start_time' => '18:00',
                    'end_time' => '22:00',
                    'start_hour' => 18,
                    'end_hour' => 22,
                ]
            ];
        }

        // Tạo danh sách giờ bắt đầu có thể chọn
        $this->generateAvailableStartTimes($bookedTimeSlots);

        // Reset selected time
        $this->selectedTime = null;
        $this->selectedStartTime = null;
        $this->selectedEndTime = null;
        $this->selectedHours = 0;
        $this->availableEndTimes = [];
    }

    // Tạo danh sách giờ bắt đầu có thể chọn
    public function generateAvailableStartTimes($bookedTimeSlots)
    {
        $this->availableStartTimes = [];

        foreach ($this->workingSchedules as $schedule) {
            $startHour = $schedule['start_hour'];
            $endHour = $schedule['end_hour'];

            // Tạo các giờ bắt đầu có thể chọn (trừ giờ cuối)
            for ($hour = $startHour; $hour < $endHour; $hour++) {
                $timeStr = sprintf('%02d:00', $hour);

                $this->availableStartTimes[] = [
                    'time' => $timeStr,
                    'display' => $timeStr,
                    'hour' => $hour,
                    'isBooked' => isset($bookedTimeSlots[$timeStr]),
                ];
            }
        }

        // Loại bỏ trùng lặp và sắp xếp
        $uniqueTimes = [];
        foreach ($this->availableStartTimes as $time) {
            $uniqueTimes[$time['time']] = $time;
        }

        ksort($uniqueTimes);
        $this->availableStartTimes = array_values($uniqueTimes);
    }

    // Chọn giờ bắt đầu
    public function selectStartTime($time)
    {
        // Kiểm tra giờ bắt đầu có hợp lệ không
        $selectedTimeSlot = null;
        foreach ($this->availableStartTimes as $availableTime) {
            if ($availableTime['time'] === $time) {
                $selectedTimeSlot = $availableTime;
                break;
            }
        }

        if (!$selectedTimeSlot) {
            $this->dispatch('error-message', message: 'Giờ bắt đầu không hợp lệ.');
            return;
        }

        if ($selectedTimeSlot['isBooked']) {
            $this->dispatch('error-message', message: "Giờ {$time} đã có người đặt. Vui lòng chọn giờ khác.");
            return;
        }

        $this->selectedStartTime = $time;
        $this->generateAvailableEndTimes();

        // Reset giờ kết thúc và số giờ
        $this->selectedEndTime = null;
        $this->selectedHours = 0;
        $this->selectedTime = null;
    }

    // Tạo danh sách giờ kết thúc có thể chọn
    public function generateAvailableEndTimes()
    {
        $this->availableEndTimes = [];

        if (!$this->selectedStartTime) {
            return;
        }

        $startHour = (int) substr($this->selectedStartTime, 0, 2);

        // Tìm khung giờ làm việc chứa giờ bắt đầu
        $maxEndHour = null;
        foreach ($this->workingSchedules as $schedule) {
            if ($startHour >= $schedule['start_hour'] && $startHour < $schedule['end_hour']) {
                $maxEndHour = $schedule['end_hour'];
                break;
            }
        }

        if (!$maxEndHour) {
            return;
        }

        // Lấy thông tin ngày đã chọn để kiểm tra xung đột
        $selectedDate = Carbon::createFromDate($this->currentYear, $this->currentMonth, $this->selectedDate);
        $dateStr = $selectedDate->format('Y-m-d');

        // Lấy các booking đã xác nhận trong ngày
        $confirmedBookings = \App\Models\Booking::where('kol_id', $this->productId)
            ->where('status', 'confirmed')
            ->whereDate('start_time', $dateStr)
            ->get();

        // Tìm giờ kết thúc tối đa có thể chọn (bị giới hạn bởi booking gần nhất)
        $maxAllowedEndHour = $maxEndHour;

        foreach ($confirmedBookings as $booking) {
            $bookingStartHour = Carbon::parse($booking->start_time)->hour;

            // Nếu booking bắt đầu sau giờ start đã chọn, nó sẽ giới hạn giờ end
            if ($bookingStartHour > $startHour) {
                $maxAllowedEndHour = min($maxAllowedEndHour, $bookingStartHour);
            }
        }

        // Tạo các giờ kết thúc có thể chọn
        for ($hour = $startHour + 1; $hour <= $maxAllowedEndHour; $hour++) {
            $timeStr = sprintf('%02d:00', $hour);

            $this->availableEndTimes[] = [
                'time' => $timeStr,
                'display' => $timeStr,
                'hour' => $hour,
            ];
        }
    }

    // Chọn giờ kết thúc
    public function selectEndTime($time)
    {
        // Kiểm tra giờ kết thúc có trong danh sách hợp lệ không
        $isValid = false;
        foreach ($this->availableEndTimes as $availableTime) {
            if ($availableTime['time'] === $time) {
                $isValid = true;
                break;
            }
        }

        if (!$isValid) {
            $this->dispatch('error-message', message: 'Giờ kết thúc không hợp lệ.');
            return;
        }

        // Vì đã lọc trước, giờ này chắc chắn hợp lệ
        $this->selectedEndTime = $time;
        $this->calculateHours();
        $this->updateSelectedTime();
    }

    // Tính số giờ
    public function calculateHours()
    {
        if ($this->selectedStartTime && $this->selectedEndTime) {
            // Lấy giờ từ string (ví dụ: "19:00" -> 19)
            $startHour = (int) substr($this->selectedStartTime, 0, 2);
            $endHour = (int) substr($this->selectedEndTime, 0, 2);

            // Tính số giờ đơn giản
            if ($endHour > $startHour) {
                $this->selectedHours = $endHour - $startHour;
            } else {
                $this->selectedHours = 0;
            }
        } else {
            $this->selectedHours = 0;
        }
    }

    // Cập nhật thông tin thời gian đã chọn
    public function updateSelectedTime()
    {
        if ($this->selectedStartTime && $this->selectedEndTime) {
            $this->selectedTime = $this->selectedStartTime . ' - ' . $this->selectedEndTime;
        } else {
            $this->selectedTime = null;
        }
    }

    public function submitBooking()
    {
        // Kiểm tra xem đã chọn ngày và giờ chưa
        if (! $this->selectedDate || ! $this->selectedStartTime || ! $this->selectedEndTime || $this->selectedHours <= 0) {
            $this->dispatch('error-message', message: 'Vui lòng chọn ngày, giờ bắt đầu và giờ kết thúc trước khi tiếp tục');
            return;
        }

        // Bật trạng thái loading
        $this->isLoading = true;

        // Kiểm tra xem có xung đột với đơn đặt khác không
        $selectedDate = Carbon::createFromDate($this->currentYear, $this->currentMonth, $this->selectedDate);
        $dateStr = $selectedDate->format('Y-m-d');

        $conflictingBooking = \App\Models\Booking::where('kol_id', $this->productId)
            ->where('status', 'confirmed')
            ->whereDate('start_time', $dateStr)
            ->where(function($query) {
                $selectedStart = $this->selectedStartTime;
                $selectedEnd = $this->selectedEndTime;

                $query->where(function($q) use ($selectedStart, $selectedEnd) {
                    // Kiểm tra xung đột thời gian
                    $q->whereTime('start_time', '<', $selectedEnd)
                      ->whereTime('end_time', '>', $selectedStart);
                });
            })
            ->first();

        if ($conflictingBooking) {
            $conflictStart = Carbon::parse($conflictingBooking->start_time)->format('H:i');
            $conflictEnd = Carbon::parse($conflictingBooking->end_time)->format('H:i');
            $conflictDate = Carbon::parse($conflictingBooking->start_time)->format('d/m/Y');

            $message = "KOL đã có lịch từ {$conflictStart} - {$conflictEnd} ngày {$conflictDate}. ";
            $message .= "Thời gian bạn chọn ({$this->selectedStartTime} - {$this->selectedEndTime}) bị trùng lặp. ";
            $message .= "Vui lòng chọn thời gian khác.";

            $this->dispatch('error-message', message: $message);
            $this->isLoading = false;
            return;
        }

        // Tạo ngày đầy đủ để lưu vào session (dạng d/m/Y cho hiển thị)
        $displayDate = Carbon::createFromDate($this->currentYear, $this->currentMonth, $this->selectedDate)->format('d/m/Y');

        // Tính tổng giá dựa trên số giờ đã chọn
        $hourlyRate = $this->kol ? $this->kol->hourly_rate : 150000;
        $totalPrice = $hourlyRate * $this->selectedHours;

        // Tạo một đối tượng Booking tạm thời để tạo mã đặt lịch
        $tempBooking = new Booking;
        $bookingCode = $tempBooking->generateUniqueCode('', [], 0);

        // Lưu thông tin đặt lịch vào session để sử dụng ở trang payment
        session([
            'booking' => [
                'kol_id' => $this->productId,
                'kol_name' => $this->kol ? $this->kol->name : 'KOL',
                'date' => $displayDate,
                'time' => $this->selectedTime,
                'start_time' => $this->selectedStartTime,
                'end_time' => $this->selectedEndTime,
                'hours' => $this->selectedHours,
                'hourly_rate' => $hourlyRate,
                'price' => $totalPrice,
                'code' => $bookingCode,
            ],
        ]);

        // Tắt trạng thái loading và chuyển hướng đến trang payment
        $this->isLoading = false;

        // Sử dụng JavaScript redirect thay vì Laravel redirect trong modal
        $this->dispatch('redirect-to-payment');
    }

    public function render()
    {
        return view('livewire.product-detail');
    }
}
