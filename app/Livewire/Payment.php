<?php

namespace App\Livewire;

use App\Models\Booking;
use App\Models\Customer;
use App\Models\Kol;
use Carbon\Carbon;
use Livewire\Component;

class Payment extends Component
{
    public $fullName;

    public $phone;

    public $email;

    public $totalAmount;

    public $amountToPay;

    public $rawAmount; // Số tiền dạng số nguyên

    public $isLoading = false;

    // Thông tin đặt lịch
    public $booking;

    public $kolName;

    public $bookingDate;

    public $bookingTime;

    public $startTime; // Giờ bắt đầu

    public $endTime; // Giờ kết thúc

    public $bookingHours;

    public $bookingPrice; // Tổng giá

    public $hourlyRate; // Giá theo giờ

    public $bookingCode;

    // Mount method runs when component is initialized
    public function mount()
    {
        // Lấy thông tin đặt lịch từ session
        $this->booking = session('booking');

        if ($this->booking) {
            $this->kolName = $this->booking['kol_name'];
            $this->bookingDate = $this->booking['date'];
            $this->bookingTime = $this->booking['time'] ? $this->booking['time'] : '20:00 - 21:00';
            $this->startTime = $this->booking['start_time'] ?? '20:00';
            $this->endTime = $this->booking['end_time'] ?? '21:00';
            $this->bookingHours = $this->booking['hours'];

            // Lấy giá theo giờ và tổng giá
            $this->hourlyRate = $this->booking['hourly_rate'] ?? 150000;
            $this->bookingPrice = $this->booking['price']; // Đây là tổng giá đã tính
            $this->bookingCode = $this->booking['code'] ?? 'BOOKING-'.time();

            // Sử dụng tổng giá đã tính từ ProductDetail
            $this->rawAmount = $this->bookingPrice;
            $this->totalAmount = number_format($this->bookingPrice, 0, ',', '.').'đ';
            $this->amountToPay = $this->totalAmount;
        } else {
            // Nếu không có thông tin đặt lịch, đánh dấu để redirect
            $this->booking = null;
            $this->dispatch('redirect-to-home');
        }
    }

    // Method to confirm order and create booking
    public function confirmOrder()
    {
        // Bật trạng thái loading
        $this->isLoading = true;

        // Validate form inputs
        $this->validate([
            'fullName' => 'required',
            'phone' => 'required',
            'email' => 'required|email',
        ], [
            'fullName.required' => 'Vui lòng nhập họ và tên',
            'phone.required' => 'Vui lòng nhập số điện thoại',
            'email.required' => 'Vui lòng nhập email',
            'email.email' => 'Email không hợp lệ',
        ]);

        // Kiểm tra thông tin đặt lịch
        if (! $this->booking) {
            session()->flash('error', 'Không tìm thấy thông tin đặt lịch');

            return;
        }

        try {
            // Tìm hoặc tạo mới khách hàng
            $customer = Customer::firstOrCreate(
                ['email' => $this->email],
                [
                    'name' => $this->fullName,
                    'phone' => $this->phone,
                ]
            );

            // Tạo đơn đặt lịch mới
            $kol = Kol::find($this->booking['kol_id']);
            if (! $kol) {
                session()->flash('error', 'Không tìm thấy thông tin KOL');

                return;
            }

            // Tạo thời gian bắt đầu và kết thúc
            $dateFormat = Carbon::createFromFormat('d/m/Y', $this->bookingDate);
            $startTime = Carbon::createFromFormat('d/m/Y H:i', $this->bookingDate.' '.$this->startTime);
            $endTime = Carbon::createFromFormat('d/m/Y H:i', $this->bookingDate.' '.$this->endTime);

            // Kiểm tra xem KOL đã có lịch đã xác nhận trong khoảng thời gian này chưa
            $existingBooking = Booking::where('kol_id', $kol->id)
                ->where('status', 'confirmed') // Chỉ kiểm tra các đơn đã xác nhận
                ->where(function ($query) use ($startTime, $endTime) {
                    // Kiểm tra các trường hợp chồng chéo thời gian
                    $query->where(function ($q) use ($startTime) {
                        // Trường hợp 1: Thời gian bắt đầu nằm trong khoảng thời gian đã đặt
                        $q->where('start_time', '<=', $startTime)
                            ->where('end_time', '>', $startTime);
                    })->orWhere(function ($q) use ($endTime) {
                        // Trường hợp 2: Thời gian kết thúc nằm trong khoảng thời gian đã đặt
                        $q->where('start_time', '<', $endTime)
                            ->where('end_time', '>=', $endTime);
                    })->orWhere(function ($q) use ($startTime, $endTime) {
                        // Trường hợp 3: Khoảng thời gian mới bao trùm khoảng thời gian đã đặt
                        $q->where('start_time', '>=', $startTime)
                            ->where('end_time', '<=', $endTime);
                    });
                })
                ->first();

            if ($existingBooking) {
                session()->flash('error', 'KOL đã có lịch trong khoảng thời gian này. Vui lòng chọn thời gian khác.');
                $this->isLoading = false;

                return;
            }

            // Tạo đơn đặt lịch
            $booking = Booking::create([
                'code' => $this->bookingCode,
                'kol_id' => $kol->id,
                'customer_id' => $customer->id,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'total_price' => $this->bookingPrice, // Sử dụng tổng giá đã tính
                'status' => 'pending', // Trạng thái chờ xác nhận
                'notes' => 'Đặt lịch từ website',
            ]);

            // Xóa thông tin đặt lịch khỏi session
            session()->forget('booking');

            // Lưu số tiền vào session để sử dụng ở trang booking-success
            session()->flash('amount', $this->rawAmount);

            // Tắt trạng thái loading và chuyển hướng đến trang cảm ơn với mã đặt lịch trong URL
            $this->isLoading = false;
            session()->flash('message', 'Đặt lịch thành công! Vui lòng chuyển khoản theo thông tin đã cung cấp để hoàn tất đặt lịch. Chúng tôi sẽ liên hệ với bạn sau khi nhận được thanh toán.');

            return redirect()->route('booking.success', ['code' => $this->bookingCode]);
        } catch (\Exception $e) {
            session()->flash('error', 'Có lỗi xảy ra: '.$e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.payment');
    }
}
