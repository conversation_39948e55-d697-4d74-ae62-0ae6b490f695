<?php

namespace App\Livewire;

use App\Models\Kol;
use Artesaos\SEOTools\Traits\SEOTools as SEOToolsTrait;
use Livewire\Attributes\Url;
use Livewire\Component;

class ProductList extends Component
{
    use SEOToolsTrait;

    // <PERSON><PERSON><PERSON> thuộc tính tìm kiếm và lọc
    #[Url]
    public $search = '';

    #[Url]
    public $status = '';

    #[Url]
    public $selectedTags = [];

    public $category = '';

    public $services = [];

    public $categories = [];

    // Số bản ghi mỗi trang (3 item mỗi dòng x 4 dòng = 12 item)
    public $perPage = 12;

    // Trang hiện tại
    #[Url]
    public $page = 1;

    // Tổng số trang
    public $totalPages = 1;

    // Tổng số bản ghi
    public $totalRecords = 0;

    // Danh sách KOL đã phân trang
    public $kols = [];

    public function mount()
    {
        // Không còn sử dụng dịch vụ
        $this->services = [];

        // L<PERSON>y danh sách danh mục
        $this->categories = [
            'Game thủ', 'Streamer', 'Idol', 'Cosplayer', 'Người mẫu', 'Ca sĩ', 'Diễn viên', 'Vũ công',
            'Nghệ sĩ', 'KOL', 'Influencer', 'Content Creator', 'Reviewer', 'Blogger', 'Vlogger',
        ];

        // Lấy danh sách KOL
        $this->loadKols();
    }

    public function loadKols()
    {
        $query = Kol::query()
            ->when($this->search, function ($query) {

                return $query->where(function ($q) {
                    $q->where('name', 'like', '%'.$this->search.'%')
                        ->orWhere('nickname', 'like', '%'.$this->search.'%');
                });
            })
            ->when($this->status, function ($query) {
                return  $query->where('is_active', $this->status === 'online');
            })
            ->when($this->category, function ($query) {
                return $query->orWhereJsonContains('categories', $this->category);
            })

        ;
//        if (! empty($this->search)) {
//            $query->where(function ($q) {
//                $q->where('name', 'like', '%'.$this->search.'%')
//                    ->orWhere('nickname', 'like', '%'.$this->search.'%');
//            });
//        }

        // Lọc theo trạng thái
//        if (! empty($this->status)) {
//            $query->where('is_active', $this->status === 'online');
//        }
//
//        // Lọc theo danh mục
//        if (! empty($this->selectedTags)) {
//            $query->where(function ($q) {
//                foreach ($this->selectedTags as $tag) {
//                    $q->orWhereJsonContains('categories', $tag);
//                }
//            });
//        }
//        if (!empty($this->category)) {
//            $query->orWhereJsonContains('categories', $this->category);
//
//        }

        // Tính tổng số bản ghi
        $this->totalRecords = $query->count();

        // Tính tổng số trang
        $this->totalPages = ceil($this->totalRecords / $this->perPage);

        // Đảm bảo trang hiện tại không vượt quá tổng số trang
        if ($this->page > $this->totalPages && $this->totalPages > 0) {
            $this->page = $this->totalPages;
        }

        // Tính offset cho phân trang
        $offset = ($this->page - 1) * $this->perPage;

        // Lấy dữ liệu đã phân trang
        $this->kols = $query->skip($offset)->take($this->perPage)->get();
    }

    public function updatedSearch()
    {
        $this->page = 1;
        $this->loadKols();
    }

    public function updatedStatus()
    {
        $this->page = 1;
        $this->loadKols();
    }

    public function updatedSelectedTags()
    {
        $this->page = 1;
        $this->loadKols();
    }

    public function toggleTag($tag)
    {
        if (in_array($tag, $this->selectedTags)) {
            $this->selectedTags = array_diff($this->selectedTags, [$tag]);
        } else {
            $this->selectedTags[] = $tag;
        }

        $this->page = 1;
        $this->loadKols();
    }

    public function resetFilters()
    {
        $this->search = '';
        $this->status = '';
        $this->category = '';
        $this->selectedTags = [];

        $this->page = 1;

        $this->loadKols();
    }

    public function goToPage($page)
    {
        $this->page = max(1, min($page, $this->totalPages));
        $this->loadKols();
    }

    public function previousPage()
    {
        if ($this->page > 1) {
            $this->page--;
            $this->loadKols();
        }
    }

    public function nextPage()
    {
        if ($this->page < $this->totalPages) {
            $this->page++;
            $this->loadKols();
        }
    }

    public function render()
    {
        $this->seo()->setTitle('Nhóm sản phẩm');
        $this->seo()->opengraph()->setSiteName(get_config('site_name'));

        return view('livewire.product-list');
    }
}
