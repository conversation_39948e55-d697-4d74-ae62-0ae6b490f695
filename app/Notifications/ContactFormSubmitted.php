<?php

namespace App\Notifications;

use App\Models\Contact;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ContactFormSubmitted extends Notification implements ShouldQueue
{
    use Queueable;

    protected $contact;

    public function __construct(Contact $contact)
    {
        $this->contact = $contact;
    }

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        $mailMessage = (new MailMessage)
            ->subject($this->getSubject())
            ->greeting('Thông tin liên hệ mới');

        if ($this->contact->type === Contact::TYPE_BRAND) {
            $mailMessage->line('Thông tin thương hiệu:')
                ->line('Tên: ' . $this->contact->data['name'])
                ->line('Email: ' . $this->contact->data['email'])
                ->line('<PERSON><PERSON> điện thoại: ' . $this->contact->data['phone'])
                ->line('Công ty: ' . $this->contact->data['company'])
                ->line('Yêu cầu hỗ trợ: ' . $this->contact->data['help'])
                ->line('Biết đến từ: ' . ($this->contact->data['hear'] ?? 'Không có'));
        } else {
            $mailMessage->line('Thông tin người sáng tạo:')
                ->line('Tên: ' . $this->contact->data['name'])
                ->line('Email: ' . $this->contact->data['email'])
                ->line('Số điện thoại: ' . $this->contact->data['phone'])
                ->line('Ngày sinh: ' . $this->contact->data['birth'])
                ->line('Giới tính: ' . $this->contact->data['gender'])
                ->line('Liên kết mạng xã hội: ' . $this->contact->data['link'])
                ->line('Lý do nộp đơn: ' . $this->contact->data['reason'])
                ->line('Biết đến từ: ' . ($this->contact->data['hear'] ?? 'Không có'));
        }

        return $mailMessage->line('Thời gian gửi: ' . $this->contact->created_at->format('d/m/Y H:i:s'));
    }

    protected function getSubject(): string
    {
        return $this->contact->type === Contact::TYPE_BRAND 
            ? 'Liên hệ mới từ Thương hiệu: ' . $this->contact->data['name']
            : 'Liên hệ mới từ Người sáng tạo: ' . $this->contact->data['name'];
    }
}