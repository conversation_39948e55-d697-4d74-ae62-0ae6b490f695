<?php

use App\Services\CacheService;
use Illuminate\Support\Facades\Storage;

function cache_service(): CacheService
{
    return app(CacheService::class);
}

function get_config($key)
{
    return cache_service()->getConfig($key);
}

function site_name()
{
    return get_config('site_name');
}

function site_logo()
{
    $logo = get_config('logo');

    return $logo ? Storage::url($logo) : null;
}

function favicon()
{
    $favicon = get_config('fav_icon');
    return $favicon ? Storage::url($favicon) : null;
}
