<?php

namespace App\Models;

use Afatmustafa\SeoSuite\Models\Traits\InteractsWithSeoSuite;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Kol extends Model
{
    use HasFactory;
    use InteractsWithSeoSuite;

    protected ?array $seoFallbacks = [
        'title' => 'name',
        'description' => 'bio',
    ];

    protected $fillable = [
        'name',
        'nickname',
        'gender',
        'email',
        'phone',
        'bio',
        'expertise',
        'categories',
        'achievements',
        'avatar',
        'gallery',
        'hourly_rate',
        'is_active',
        'social_links',
        'schedule',
    ];

    protected $casts = [
        'social_links' => 'array',
        'categories' => 'array',
        'gallery' => 'array',
        'schedule' => 'array',
        'is_active' => 'boolean',
        'hourly_rate' => 'decimal:2',
        'expertise' => 'array',
    ];

    public function schedules(): HasMany
    {
        return $this->hasMany(Schedule::class);
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }
    public function reviews()
    {
        return $this->hasMany(Review::class);
    }
}
