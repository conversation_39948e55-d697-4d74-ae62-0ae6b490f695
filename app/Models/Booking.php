<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use WillVincent\LaravelUnique\HasUniqueNames;

class Booking extends Model
{
    use HasFactory, HasUniqueNames;

    protected $uniqueField = 'code';

    protected $uniqueValueGenerator = 'generateUniqueCode';

    protected $fillable = [
        'code',
        'kol_id',
        'customer_id',
        'start_time',
        'end_time',
        'total_price',
        'status',
        'notes',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'total_price' => 'decimal:2',
    ];

    public function kol(): BelongsTo
    {
        return $this->belongsTo(Kol::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function payment(): HasOne
    {
        return $this->hasOne(Payment::class);
    }

    /**
     * Generate a unique booking code
     * 8 uppercase characters + 8 digits from timestamp to avoid duplicates
     */
    public function generateUniqueCode(string $base, array $constraints, ?int $attempt): string
    {
        // Generate a random 8-character uppercase string
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $code = '';

        for ($i = 0; $i < 8; $i++) {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        }

        // Add 8 digits from timestamp to ensure uniqueness
        $timestamp = (string) time();
        $code .= substr($timestamp, -8); // Take the last 8 digits of the timestamp

        return $code;
    }
}
