{"name": "afatmustafa/seo-suite-clone", "require": {"php": "^8.1", "artesaos/seotools": "^1.3", "spatie/laravel-package-tools": "^1.15.0"}, "require-dev": {"filament/filament": "^3.2", "laravel/pint": "^1.0", "nunomaduro/collision": "^7.9", "nunomaduro/larastan": "^2.0.1", "orchestra/testbench": "^8.0", "pestphp/pest": "^2.1", "pestphp/pest-plugin-arch": "^2.0", "pestphp/pest-plugin-laravel": "^2.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0"}, "autoload": {"psr-4": {"Afatmustafa\\SeoSuite\\": "src/", "Afatmustafa\\SeoSuite\\Database\\Factories\\": "database/factories/"}}, "autoload-dev": {"psr-4": {"Afatmustafa\\SeoSuite\\Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": "@php ./vendor/bin/testbench package:discover --ansi", "analyse": "vendor/bin/phpstan analyse", "test": "vendor/bin/pest", "test-coverage": "vendor/bin/pest --coverage", "format": "vendor/bin/pint"}, "config": {"sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "phpstan/extension-installer": true}}, "extra": {"laravel": {"providers": ["Afatmustafa\\SeoSuite\\SeoSuiteServiceProvider"], "aliases": {"SeoSuite": "Afatmustafa\\SeoSuite\\Facades\\SeoSuite"}}}, "minimum-stability": "dev", "prefer-stable": true}