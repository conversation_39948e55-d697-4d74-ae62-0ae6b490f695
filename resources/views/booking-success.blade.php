<x-client-layout>
    <x-slot:title>
        @if($bookingCode && $bookingCode != 'BOOKING')
            Đặt lịch thành công - {{ $bookingCode }}
        @else
            Đặt lịch thành công
        @endif
    </x-slot:title>

    <x-slot:styles>
        <!-- CSS cho trang thành công -->
        <style>
            .success-container {
                max-width: 800px;
                margin: 120px auto 50px;
                padding: 30px;
                background-color: #fff;
                border-radius: 10px;
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
                text-align: center;
            }

            .success-icon {
                font-size: 80px;
                color: #4CAF50;
                margin-bottom: 20px;
            }

            .success-title {
                font-size: 28px;
                font-weight: 700;
                margin-bottom: 10px;
                color: #333;
            }

            .success-code {
                font-size: 18px;
                margin-bottom: 20px;
                color: #4CAF50;
                background-color: #f0f8f0;
                display: inline-block;
                padding: 8px 15px;
                border-radius: 5px;
            }

            .success-message {
                font-size: 18px;
                line-height: 1.6;
                color: #666;
                margin-bottom: 30px;
            }

            .success-details {
                background-color: #f9f9f9;
                padding: 20px;
                border-radius: 8px;
                margin-bottom: 30px;
                text-align: left;
            }

            .success-details h2 {
                margin-bottom: 15px;
                font-size: 20px;
                color: #333;
            }

            .success-details p {
                margin-bottom: 20px;
                color: #666;
            }

            .detail-item {
                margin-bottom: 15px;
                display: flex;
                align-items: center;
            }

            .detail-item:last-child {
                margin-bottom: 0;
            }

            .detail-label {
                font-weight: 600;
                width: 150px;
                color: #555;
            }

            .home-button {
                display: inline-block;
                background-color: #4CAF50;
                color: white;
                padding: 12px 30px;
                border-radius: 5px;
                text-decoration: none;
                font-weight: 600;
                transition: background-color 0.3s;
            }

            .home-button:hover {
                background-color: #45a049;
            }

            .payment-methods {
                margin-top: 25px;
                border-top: 1px dashed #ddd;
                padding-top: 20px;
            }

            .payment-methods-title {
                font-size: 18px;
                color: #333;
                margin-bottom: 15px;
                font-weight: 600;
            }

            .payment-method-tabs {
                display: flex;
                margin-bottom: 15px;
                border-bottom: 1px solid #eee;
            }

            .payment-method-tab {
                padding: 10px 15px;
                cursor: pointer;
                border: 1px solid #eee;
                border-bottom: none;
                border-radius: 5px 5px 0 0;
                margin-right: 5px;
                font-size: 14px;
                background-color: #f9f9f9;
            }

            .payment-method-tab.active {
                background-color: #4CAF50;
                color: white;
                border-color: #4CAF50;
            }

            .payment-method-content {
                padding: 15px;
                background-color: #f9f9f9;
                border-radius: 0 5px 5px 5px;
            }

            .payment-method-description {
                margin-bottom: 0;
                color: #666;
                font-size: 14px;
            }

            .qr-code-container {
                text-align: center;
                padding: 15px;
            }

            .qr-code-image {
                max-width: 200px;
                height: auto;
                border-radius: 5px;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            }

            .qr-note {
                font-size: 12px;
                color: #666;
                margin-top: 10px;
                font-style: italic;
            }
        </style>
    </x-slot:styles>

    <x-slot:scripts>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Lấy các phần tử tab và nội dung
                const bankTransferTab = document.getElementById('bank-transfer-tab');
                const qrCodeTab = document.getElementById('qr-code-tab');
                const bankTransferContent = document.getElementById('bank-transfer-content');
                const qrCodeContent = document.getElementById('qr-code-content');

                // Xử lý sự kiện click cho tab chuyển khoản
                bankTransferTab.addEventListener('click', function() {
                    // Kích hoạt tab chuyển khoản
                    bankTransferTab.classList.add('active');
                    qrCodeTab.classList.remove('active');

                    // Hiển thị nội dung chuyển khoản, ẩn nội dung QR
                    bankTransferContent.style.display = 'block';
                    qrCodeContent.style.display = 'none';
                });

                // Xử lý sự kiện click cho tab QR
                qrCodeTab.addEventListener('click', function() {
                    // Kích hoạt tab QR
                    qrCodeTab.classList.add('active');
                    bankTransferTab.classList.remove('active');

                    // Hiển thị nội dung QR, ẩn nội dung chuyển khoản
                    qrCodeContent.style.display = 'block';
                    bankTransferContent.style.display = 'none';
                });
            });
        </script>
    </x-slot:scripts>

    <div class="success-container">
        <div class="success-icon">
            <i class="fas fa-check-circle"></i>
        </div>

        <h1 class="success-title">Đặt lịch thành công!</h1>
        @if($bookingCode && $bookingCode != 'BOOKING')
        <p class="success-code">Mã đặt lịch: <strong>{{ $bookingCode }}</strong></p>
        @endif

        <p class="success-message">
            Cảm ơn bạn đã đặt lịch với chúng tôi. Chúng tôi đã nhận được thông tin đặt lịch của bạn và sẽ liên hệ với bạn trong thời gian sớm nhất để xác nhận.
        </p>

        <div class="success-details">
            <h2>Thông tin chuyển khoản</h2>
            <p>Vui lòng chuyển khoản theo thông tin sau để hoàn tất đặt lịch:</p>

            <div class="detail-item">
                <span class="detail-label">Tên tài khoản:</span>
                <span>CÔNG TY TNHH PI CORPORATION</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Ngân hàng:</span>
                <span>Vietcombank - Chi nhánh Hà Nội</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Số tài khoản:</span>
                <span>*************</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Nội dung CK:</span>
                @if($bookingCode && $bookingCode != 'BOOKING')
                <span><strong>{{ $bookingCode }}</strong></span>
                @else
                <span>BOOKING [Tên của bạn]</span>
                @endif
            </div>

            <div class="payment-methods">
                <h3 class="payment-methods-title">Phương thức thanh toán</h3>

                <div class="payment-method-tabs">
                    <div class="payment-method-tab active" id="bank-transfer-tab">Chuyển khoản ngân hàng</div>
                    <div class="payment-method-tab" id="qr-code-tab">Quét mã QR</div>
                </div>

                <div class="payment-method-content" id="bank-transfer-content">
                    <p class="payment-method-description">Chuyển khoản theo thông tin bên trên và ghi rõ nội dung chuyển khoản.</p>
                </div>

                <div class="payment-method-content" id="qr-code-content" style="display: none;">
                    <div class="qr-code-container">
                        @if($bookingCode && $bookingCode != 'BOOKING')
                            <img src="https://img.vietqr.io/image/{{get_config('bank_code')}}-{{get_config('bank_account')}}-compact2.png?addInfo={{ urlencode($bookingCode) }}&accountName=C%C3%94NG%20TY%20TNHH%20PI%20CORPORATION"
                                 alt="Mã QR thanh toán" class="qr-code-image">
                        @else
                            <img src="https://img.vietqr.io/image/{{get_config('bank_code')}}-{{get_config('bank_account')}}.png?addInfo=BOOKING&accountName=C%C3%94NG%20TY%20TNHH%20PI%20CORPORATION"
                                 alt="Mã QR thanh toán" class="qr-code-image">
                        @endif
                        <p class="qr-note">Sử dụng ứng dụng ngân hàng hoặc ví điện tử hỗ trợ VietQR để quét mã</p>
                    </div>
                </div>
            </div>
        </div>

        @if (session()->has('message'))
            <div class="alert alert-success">
                {{ session('message') }}
            </div>
        @endif

        <a href="{{ route('home') }}" class="home-button">Quay lại trang chủ</a>
    </div>
</x-client-layout>
