<div>
    <div class="product-detail-container">
        <div class="container">
            <!-- Header Section with Bootstrap grid 3-6-3 -->
            <div class="product-header">
                <div class="row">
                    <!-- Column 1: Slideshow, Status, Achievements (col-md-3) -->
                    <div class="col-12 col-md-3 mb-4 mb-md-0">
                        <!-- Slideshow -->
                        <div class="product-slideshow" wire:poll.{{ $autoSlideInterval }}ms="autoAdvanceSlide">
                            <div class="slide-container">
                                @if($kol)
                                    <!-- Ảnh đại diện là slide đầu tiên -->
                                    <div class="slide {{ $currentHeaderSlide === 0 ? 'active' : '' }}">
                                        @if($kol->avatar)
                                            <img src="{{ asset('storage/' . $kol->avatar) }}" alt="{{ $kol->name }}">
                                        @else
                                            <img src="{{ asset('client/assets/img/creator/creator_list/demo.png') }}"
                                                 alt="{{ $kol->name }}">
                                        @endif
                                    </div>

                                    <!-- <PERSON><PERSON><PERSON> trong gallery -->
                                    @if($kol->gallery)
                                        @foreach($kol->gallery as $index => $image)
                                            <div class="slide {{ $currentHeaderSlide === $index + 1 ? 'active' : '' }}">
                                                <img src="{{ asset('storage/' . $image) }}"
                                                     alt="{{ $kol->name }} Gallery {{ $index + 1 }}">
                                            </div>
                                        @endforeach
                                    @endif
                                @else
                                    <!-- Dữ liệu mẫu nếu không có KOL -->
                                    <div class="slide {{ $currentHeaderSlide === 0 ? 'active' : '' }}">
                                        <img src="{{ asset('client/assets/img/creator/creator_list/demo.png') }}"
                                             alt="Slide 1">
                                    </div>
                                    <div class="slide {{ $currentHeaderSlide === 1 ? 'active' : '' }}">
                                        <img src="{{ asset('client/assets/img/creator/creator_list/demo.png') }}"
                                             alt="Slide 2">
                                    </div>
                                    <div class="slide {{ $currentHeaderSlide === 2 ? 'active' : '' }}">
                                        <img src="{{ asset('client/assets/img/creator/creator_list/demo.png') }}"
                                             alt="Slide 3">
                                    </div>
                                @endif

                                <!-- Nút điều hướng -->
                                <a class="prev" wire:click="changeHeaderSlide(-1)">&#10094;</a>
                                <a class="next" wire:click="changeHeaderSlide(1)">&#10095;</a>

                                <div class="slide-nav">
                                    @if($kol && $kol->gallery)
                                        <!-- Chỉ báo cho ảnh đại diện -->
                                        <span class="slide-dot {{ $currentHeaderSlide === 0 ? 'active' : '' }}"
                                              wire:click="setHeaderSlide(0)"></span>

                                        <!-- Chỉ báo cho các ảnh trong gallery -->
                                        @foreach($kol->gallery as $index => $image)
                                            <span
                                                class="slide-dot {{ $currentHeaderSlide === $index + 1 ? 'active' : '' }}"
                                                wire:click="setHeaderSlide({{ $index + 1 }})"></span>
                                        @endforeach
                                    @else
                                        <!-- Chỉ báo mẫu -->
                                        <span class="slide-dot {{ $currentHeaderSlide === 0 ? 'active' : '' }}"
                                              wire:click="setHeaderSlide(0)"></span>
                                        <span class="slide-dot {{ $currentHeaderSlide === 1 ? 'active' : '' }}"
                                              wire:click="setHeaderSlide(1)"></span>
                                        <span class="slide-dot {{ $currentHeaderSlide === 2 ? 'active' : '' }}"
                                              wire:click="setHeaderSlide(2)"></span>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Status Box -->
                        <div class="status-box">
                            <h3 class="status-title">Trạng thái</h3>
                            <div class="status-item">
                                <span class="status-icon"><i class="fas fa-circle"></i></span>
                                <span>{{ $kol && $kol->is_active ? 'Đang hoạt động' : 'Không hoạt động' }}</span>
                            </div>
                            <div class="status-item">
                                <span class="status-icon"><i class="fas fa-venus-mars"></i></span>
                                <span>{{ $kol ? ($kol->gender === 'male' ? 'Nam' : ($kol->gender === 'female' ? 'Nữ' : 'Khác')) : 'Không xác định' }}</span>
                            </div>
                            @if(count($schedules) > 0)
                                <div class="status-item">
                                    <span class="status-icon"><i class="fas fa-calendar-alt"></i></span>
                                    <span>{{ $schedules[0]['day'] ?? '' }} {{ $schedules[0]['hours'] ?? '' }}</span>
                                </div>
                            @endif
                        </div>

                        <!-- Achievements Box -->
                        <div class="achievement-box">
                            <h3 class="status-title">Thành tích</h3>
                            @if($kol && $kol->achievements)
                                @php
                                    $achievementsList = explode("\n", $kol->achievements);
                                    $icons = ['trophy', 'medal', 'star', 'award', 'certificate'];
                                @endphp

                                @foreach($achievementsList as $index => $achievement)
                                    @if(trim($achievement))
                                        <div class="achievement-item">
                                            <div class="achievement-icon"><i
                                                    class="fas fa-{{ $icons[$index % count($icons)] }}"></i></div>
                                            <div class="achievement-text">{{ $achievement }}</div>
                                        </div>
                                    @endif
                                @endforeach
                            @else
                                <div class="achievement-item">
                                    <div class="achievement-icon"><i class="fas fa-trophy"></i></div>
                                    <div class="achievement-text">Top 10 game thủ được yêu thích nhất tháng 6/2023</div>
                                </div>
                                <div class="achievement-item">
                                    <div class="achievement-icon"><i class="fas fa-medal"></i></div>
                                    <div class="achievement-text">Cao thủ Liên Minh Huyền Thoại - Thách đấu</div>
                                </div>
                                <div class="achievement-item">
                                    <div class="achievement-icon"><i class="fas fa-star"></i></div>
                                    <div class="achievement-text">4.9/5 đánh giá (120 lượt đánh giá)</div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Column 2: Information (col-md-6) -->
                    <div class="col-12 col-md-6 mb-4 mb-md-0">
                        <div class="middle-content p-3">
                            <h1 class="product-name">{{ $kol->name }}</h1>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="category-tags">
                                    @if($kol && $kol->categories)
                                        @foreach($kol->categories as $category)
                                            <span class="category-tag">{{ $category }}</span>
                                        @endforeach
                                    @else
                                        <span class="category-tag">Game thủ</span>
                                        <span class="category-tag">Streamer</span>
                                        <span class="category-tag">Idol</span>
                                        <span class="category-tag">Cosplayer</span>
                                    @endif
                                </div>

                                {{--                                <div class="social-links">--}}
                                {{--                                    @if($kol && $kol->social_links)--}}
                                {{--                                        @foreach($kol->social_links as $social)--}}
                                {{--                                            <a href="{{ $social['url'] ?? '#' }}" class="social-link" target="_blank">--}}
                                {{--                                                <i class="fab fa-{{ $social['platform'] ?? 'link' }}"></i>--}}
                                {{--                                            </a>--}}
                                {{--                                        @endforeach--}}
                                {{--                                    @else--}}
                                {{--                                        <a href="#" class="social-link">--}}
                                {{--                                            <i class="fab fa-tiktok"></i>--}}
                                {{--                                        </a>--}}
                                {{--                                        <a href="#" class="social-link">--}}
                                {{--                                            <i class="fab fa-instagram"></i>--}}
                                {{--                                        </a>--}}
                                {{--                                        <a href="#" class="social-link">--}}
                                {{--                                            <i class="fab fa-youtube"></i>--}}
                                {{--                                        </a>--}}
                                {{--                                        <a href="#" class="social-link">--}}
                                {{--                                            <i class="fab fa-facebook"></i>--}}
                                {{--                                        </a>--}}
                                {{--                                    @endif--}}
                                {{--                                </div>--}}
                            </div>

                            <!-- About Section -->
                            <div class="about-section mt-4 mb-4">
                                <h2 class="section-title">Giới thiệu</h2>
                                <div class="about-text">
                                    @if($kol && $kol->bio)
                                        {!! nl2br(e($kol->bio)) !!}
                                    @else
                                        <p>Xin chào mọi người, mình là KamKam! Mình là một game thủ chuyên nghiệp với
                                            hơn 5 năm kinh nghiệm trong các tựa game MOBA và FPS. Mình có thể đồng hành
                                            cùng bạn trong các trò chơi như Liên Minh Huyền Thoại, Valorant, PUBG và
                                            nhiều game khác.</p>
                                        <p>Mình luôn đảm bảo mang đến trải nghiệm vui vẻ, thoải mái và chuyên nghiệp cho
                                            người chơi. Dù bạn muốn cải thiện kỹ năng hay chỉ đơn giản là tìm bạn đồng
                                            hành để giải trí, mình đều sẵn sàng!</p>
                                    @endif
                                </div>
                            </div>
                            <livewire:components.review :kol="$kol" lazy/>
                        </div>
                    </div>

                    <!-- Column 3: Price and Booking (col-md-3) -->
                    <div class="col-12 col-md-3">
                        <div class="price-booking-box p-3">
                            <span class="price-label">Giá thuê</span>
                            <div
                                class="product-price">{{ $kol ? number_format($kol->hourly_rate, 0, ',', '.') : '150.000' }}
                                đ / giờ
                            </div>

                            <div class="product-actions" style="flex-direction: column; width: 100%;">
                                <button class="action-button" wire:click="openBookingModal" style="width: 100%;">
                                    <span wire:loading.remove wire:target="openBookingModal">Thuê ngay</span>
                                    <span wire:loading wire:target="openBookingModal" class="loading-spinner">
                                        <svg class="animate-spin text-white inline" xmlns="http://www.w3.org/2000/svg"
                                             fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                    stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor"
                                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Thuê ngay
                                    </span>
                                </button>
                            </div>


                            <!-- Lịch hoạt động -->
                            <div class="schedule-box mt-4">
                                <div class="status-title">Lịch hoạt động</div>
                                @if(count($schedules) > 0)
                                    @foreach($schedules as $schedule)
                                        <div class="schedule-item">
                                            <div class="schedule-day">{{ $schedule['day'] ?? 'Không xác định' }}</div>
                                            <div
                                                class="schedule-time">{{ $schedule['hours'] ?? 'Không xác định' }}</div>
                                        </div>
                                    @endforeach
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Booking Modal -->
    <div class="booking-modal {{ $showBookingModal ? 'show' : '' }}">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Thuê {{$kol->name}}</h3>
                <span class="close-modal" wire:click="closeBookingModal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger" style="display: none;" id="booking-error-message">
                    Vui lòng chọn ngày và khung giờ trước khi tiếp tục
                </div>
                <div class="booking-form">
                    <div class="form-group">
                        <label class="form-label">Nội dung</label>
                        <textarea class="form-control" placeholder="Nhập nội dung bạn muốn trao đổi..."></textarea>
                    </div>
                </div>
                <div class="booking-calendar">
                    <div class="calendar-container">
                        <div class="calendar-header">
                            <div class="calendar-month">{{ $this->getCurrentMonthName() }}</div>
                            <div class="calendar-nav">
                                <button class="calendar-nav-btn" wire:click="prevMonth">&#10094;</button>
                                <button class="calendar-nav-btn" wire:click="nextMonth">&#10095;</button>
                            </div>
                        </div>
                        <div class="calendar-grid">
                            <div class="calendar-day-header">CN</div>
                            <div class="calendar-day-header">T2</div>
                            <div class="calendar-day-header">T3</div>
                            <div class="calendar-day-header">T4</div>
                            <div class="calendar-day-header">T5</div>
                            <div class="calendar-day-header">T6</div>
                            <div class="calendar-day-header">T7</div>

                            {{-- Ngày trống ở đầu tháng --}}
                            @for ($i = 0; $i < $firstDayOfMonth; $i++)
                                <div class="calendar-day disabled"></div>
                            @endfor

                            {{-- Các ngày trong tháng --}}
                            @foreach ($daysInMonth as $dayInfo)
                                <div class="calendar-day
                                    {{ ($dayInfo['isPast'] && !$dayInfo['isToday']) || !$dayInfo['isAvailable'] ? 'disabled' : '' }}
                                    {{ $dayInfo['isToday'] ? 'today' : '' }}
                                    {{ $selectedDate == $dayInfo['day'] ? 'active' : '' }}
                                    {{ !$dayInfo['isAvailable'] && !$dayInfo['isPast'] ? 'no-schedule' : '' }}"
                                     @if ($dayInfo['isAvailable'])
                                         wire:click="selectDate({{ $dayInfo['day'] }})"
                                    @endif
                                >
                                    {{ $dayInfo['day'] }}
                                    @if (!$dayInfo['isAvailable'] && !$dayInfo['isPast'])
                                        <span class="no-schedule-indicator" title="Không có lịch hoạt động"></span>
                                    @endif
                                </div>
                            @endforeach

                            {{-- Ngày trống ở cuối tháng --}}
                            @php
                                $totalDaysShown = $firstDayOfMonth + count($daysInMonth);
                                $remainingCells = ceil($totalDaysShown / 7) * 7 - $totalDaysShown;
                            @endphp

                            @for ($i = 0; $i < $remainingCells; $i++)
                                <div class="calendar-day disabled"></div>
                            @endfor
                        </div>
                    </div>

                    <div class="time-slots">
                        <div class="time-slot-title">
                            Chọn thời gian thuê
                            ngày {{ $selectedDate ? $selectedDate . '/' . $currentMonth . '/' . $currentYear : 'chưa chọn' }}
                        </div>

                        @if($selectedDate)
                            @if(count($workingSchedules) > 0)
                                <!-- Hiển thị khung giờ làm việc -->
                                <div class="working-schedule-info">
                                    <strong>Khung giờ làm việc:</strong>
                                    @foreach($workingSchedules as $schedule)
                                        <span class="schedule-range">{{ $schedule['start_time'] }} - {{ $schedule['end_time'] }}</span>
                                        @if(!$loop->last), @endif
                                    @endforeach
                                </div>

                                <!-- Chọn giờ bắt đầu -->
                                <div class="time-selection-section">
                                    <h4>1. Chọn giờ bắt đầu:</h4>
                                    <div class="time-slot-grid">
                                        @foreach($availableStartTimes as $startTime)
                                            <div class="time-slot
                                                {{ $selectedStartTime === $startTime['time'] ? 'active' : '' }}
                                                {{ $startTime['isBooked'] ? 'booked disabled' : '' }}"
                                                 @if(!$startTime['isBooked'])
                                                     wire:click="selectStartTime('{{ $startTime['time'] }}')"
                                                @endif
                                            >
                                                <div class="time-slot-time">{{ $startTime['display'] }}</div>
                                                @if($startTime['isBooked'])
                                                    <span class="booked-label">Đã đặt</span>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>
                                </div>

                                <!-- Chọn giờ kết thúc -->
                                @if($selectedStartTime)
                                    <div class="time-selection-section">
                                        <h4>2. Chọn giờ kết thúc:</h4>
                                        @if(count($availableEndTimes) > 0)
                                            <div class="time-slot-grid">
                                                @foreach($availableEndTimes as $endTime)
                                                    <div class="time-slot
                                                        {{ $selectedEndTime === $endTime['time'] ? 'active' : '' }}"
                                                         wire:click="selectEndTime('{{ $endTime['time'] }}')"
                                                    >
                                                        <div class="time-slot-time">{{ $endTime['display'] }}</div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @else
                                            <div class="no-end-times-message">
                                                <p style="color: #ff6b6b; font-style: italic; text-align: center; margin-top: 15px;">
                                                    Không có giờ kết thúc nào khả dụng cho giờ bắt đầu {{ $selectedStartTime }}.<br>
                                                    Vui lòng chọn giờ bắt đầu khác.
                                                </p>
                                            </div>
                                        @endif
                                    </div>
                                @endif

                                <!-- Hiển thị thông tin đã chọn -->
                                @if($selectedTime && $selectedHours > 0)
                                    <div class="selected-time-summary">
                                        <div class="selected-time-info">
                                            <span class="selected-time-label">Thời gian đã chọn:</span>
                                            <span class="selected-time-value">{{ $selectedTime }}</span>
                                        </div>
                                        <div class="selected-time-info">
                                            <span class="selected-time-label">Số giờ:</span>
                                            <span class="selected-time-value">{{ $selectedHours }} giờ</span>
                                        </div>
                                        <div class="selected-time-info">
                                            <span class="selected-time-label">Tạm tính:</span>
                                            <span class="selected-time-value">{{ number_format(($kol->hourly_rate ?? 150000) * $selectedHours, 0, ',', '.') }} đ</span>
                                        </div>
                                    </div>
                                @elseif($selectedStartTime && !$selectedEndTime)
                                    <div class="selection-hint">
                                        <p style="color: #ccc; font-style: italic; text-align: center; margin-top: 15px;">
                                            Vui lòng chọn giờ kết thúc để hoàn tất
                                        </p>
                                    </div>
                                @endif
                            @else
                                <div class="no-slots-message">Không có lịch làm việc cho ngày này</div>
                            @endif
                        @else
                            <div class="no-slots-message">Vui lòng chọn ngày trước</div>
                        @endif
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-submit" wire:click="submitBooking" wire:loading.attr="disabled"
                        wire:target="submitBooking">
                    <span wire:loading.remove wire:target="submitBooking">Tiếp tục</span>
                    <span wire:loading wire:target="submitBooking" class="loading-spinner">
                        <svg class="animate-spin text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none"
                             viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor"
                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Tiếp tục
                    </span>
                </button>
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Xử lý sự kiện khi chọn khung giờ (Livewire v3)
            document.addEventListener('livewire:initialized', () => {
                Livewire.on('time-selected', (data) => {
                    // Tạo hiệu ứng nhấp nháy cho khung giờ được chọn
                    const selectedTime = data.time;
                    const timeSlots = document.querySelectorAll('.time-slot');

                    timeSlots.forEach(slot => {
                        if (slot.innerText.trim() === selectedTime) {
                            // Tạo hiệu ứng nhấp nháy
                            slot.classList.add('pulse-effect');
                            setTimeout(() => {
                                slot.classList.remove('pulse-effect');
                            }, 1000);
                        }
                    });
                });

                // Xử lý hiển thị thông báo lỗi
                Livewire.on('error-message', (data) => {
                    const errorElement = document.getElementById('booking-error-message');
                    if (errorElement) {
                        errorElement.textContent = data.message;
                        errorElement.style.display = 'block';

                        // Tự động ẩn thông báo sau 5 giây
                        setTimeout(() => {
                            errorElement.style.display = 'none';
                        }, 5000);
                    }
                });

                // Xử lý redirect đến trang payment
                Livewire.on('redirect-to-payment', () => {
                    console.log('Redirecting to payment page...');
                    window.location.href = '/payment';
                });
            });
        });
    </script>
</div>
