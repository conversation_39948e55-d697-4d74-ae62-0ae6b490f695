<div class="product-list-container">
    <!-- Banner -->
    <section class="top_video">
        <h2 class="font_bold">Booking KOL <br>
            T<PERSON><PERSON> cho buổi livestream của bạn</h2>
        <video src="https://pi-corporation.s3.ap-northeast-2.amazonaws.com/creator_header.mp4"
               poster="/client/assets/img/creator/creator_header.png" muted="" autoplay="" playsinline=""
               loop=""></video>
    </section>

    <!-- Bộ lọc -->
    <div class="container-fluid">
        <div class="row p-5">
            <div class="col-md-3">
                <section class="filter-section">
                    <div class="filter-container">
                        <h2 class="filter-title">Bộ lọc</h2>

                        <!-- Tìm kiếm theo tên -->
                        <div class="filter-row">
                            <div class="filter-label">Tìm kiếm:</div>
                            <div class="filter-content">
                                <input type="text" id="filter-search" class="filter-input w-100" placeholder="Nhập tên người live..." wire:model="search">
                            </div>
                        </div>

                        <!-- Lọc theo trạng thái -->
                        <div class="filter-row">
                            <div class="filter-label">Trạng thái:</div>
                            <div class="filter-content">
                                <select id="filter-status" class="filter-select w-100" wire:model>
                                    <option value="">Tất cả</option>
                                    <option value="online">Online</option>
                                    <option value="offline">Offline</option>
                                </select>
                            </div>
                        </div>
                        <div class="filter-row">
                            <div class="filter-label">Thể loại:</div>
                            <div class="filter-content">
                                <select id="filter-status" class="filter-select w-100" wire:model="category">
                                    <option value="">Tất cả</option>
                                    @foreach($categories as $category)
                                        <option value="{{$category}}" >
                                            {{ $category }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <!-- Lọc theo thể loại -->
{{--                        <div class="filter-row">--}}
{{--                            <div class="filter-label">Thể loại:</div>--}}
{{--                            <div class="filter-content">--}}
{{--                                @foreach($categories as $category)--}}
{{--                                    <div class="filter-tag {{ in_array($category, $selectedTags) ? 'active' : '' }}"--}}
{{--                                         wire:click="toggleTag('{{ $category }}')">--}}
{{--                                        {{ $category }}--}}
{{--                                    </div>--}}
{{--                                @endforeach--}}
{{--                            </div>--}}
{{--                        </div>--}}

                        <!-- Nút lọc -->
                        <div class="filter-buttons">
                            <button class="filter-button" wire:click="loadKols">Lọc</button>
                            <button class="filter-reset" wire:click="resetFilters">Xóa bộ lọc</button>
                        </div>
                    </div>
                </section>
                <div class="pagination-info text-center">
                    Hiển thị {{ count($kols) }} trên tổng số {{ $totalRecords }} KOL | Trang {{ $page }}
                    /{{ $totalPages }}
                </div>
            </div>
            <div class="col-md-9">
                <section class="bottom_inner">
                    <div class="bottom">
                        <div class="bottom-list" id="bottom-list">
{{--                            <div class="pagination-info">--}}
{{--                                Hiển thị {{ count($kols) }} trên tổng số {{ $totalRecords }} KOL | Trang {{ $page }}/{{ $totalPages }}--}}
{{--                            </div>--}}
                            <ul id="creator-list" class="creator">
                                @forelse($kols as $kol)
                                    @livewire('kol-item', ['kol' => $kol], key($kol->id))
                                @empty
                                    <li class="no-results">
                                        <p>Không tìm thấy KOL nào phù hợp với tiêu chí tìm kiếm.</p>
                                    </li>
                                @endforelse
                            </ul>
                        </div>
                    </div>
                    <!-- Phân trang -->
                    <div class="pagination-container">
                        <ul class="pagination">
                            <!-- Nút Previous -->
                            <li class="pagination-item">
                                <button class="pagination-link {{ $page <= 1 ? 'disabled' : '' }}"
                                        wire:click="previousPage"
                                    {{ $page <= 1 ? 'disabled' : '' }}>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" d="M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z"/>
                                    </svg>
                                </button>
                            </li>

                            <!-- Các trang -->
                            @php
                                $visiblePages = 5; // Số trang hiển thị
                                $halfVisible = floor($visiblePages / 2);
                                $startPage = max(1, $page - $halfVisible);
                                $endPage = min($totalPages, $startPage + $visiblePages - 1);

                                if ($endPage - $startPage + 1 < $visiblePages) {
                                    $startPage = max(1, $endPage - $visiblePages + 1);
                                }
                            @endphp

                            @if($startPage > 1)
                                <li class="pagination-item">
                                    <button class="pagination-link" wire:click="goToPage(1)">1</button>
                                </li>
                                @if($startPage > 2)
                                    <li class="pagination-item">
                                        <span class="pagination-ellipsis">...</span>
                                    </li>
                                @endif
                            @endif

                            @for($i = $startPage; $i <= $endPage; $i++)
                                <li class="pagination-item">
                                    <button class="pagination-link {{ $i == $page ? 'active' : '' }}"
                                            wire:click="goToPage({{ $i }})">
                                        {{ $i }}
                                    </button>
                                </li>
                            @endfor

                            @if($endPage < $totalPages)
                                @if($endPage < $totalPages - 1)
                                    <li class="pagination-item">
                                        <span class="pagination-ellipsis">...</span>
                                    </li>
                                @endif
                                <li class="pagination-item">
                                    <button class="pagination-link" wire:click="goToPage({{ $totalPages }})">{{ $totalPages }}</button>
                                </li>
                            @endif

                            <!-- Nút Next -->
                            <li class="pagination-item">
                                <button class="pagination-link {{ $page >= $totalPages ? 'disabled' : '' }}"
                                        wire:click="nextPage"
                                    {{ $page >= $totalPages ? 'disabled' : '' }}>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                        <path fill-rule="evenodd" d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/>
                                    </svg>
                                </button>
                            </li>
                        </ul>
                    </div>

                    <a class="livepage_btn font_extrabold" href="#" target="_blank"><span
                            class="eng_font font_bold">Live Creators</span><span>Xem thêm</span></a>
                </section>
            </div>
        </div>
    </div>
</div>


