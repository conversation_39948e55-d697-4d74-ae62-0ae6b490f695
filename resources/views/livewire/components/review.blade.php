<?php

use Livewire\WithPagination;
use function Livewire\Volt\{state, mount};
use Livewire\Volt\Component;

new class extends Component {
    use WithPagination;
    protected $paginationTheme = 'bootstrap';

    public $kol;
    public $name;
    public $content;
    public $rating = 5;


    public function submitRating()
    {
        try {
            $this->validate([
                'name' => 'required',
                'rating' => 'required|numeric|between:1,5',
            ], [
                'name.required' => 'Vui lòng nhập tên',
                'rating.between' => 'Vui lòng chọn đúng số sao',
            ]);
            \App\Models\Review::create([
                'customer_name' => $this->name,
                'content' => strip_tags($this->content),
                'rating' => $this->rating,
                'kol_id' => $this->kol->id,
                'status' => 0
            ]);
            $this->reset('name', 'content');
            $this->rating = 5;
            $this->js("alert('<PERSON><PERSON><PERSON> thành công, đ<PERSON>h gi<PERSON> của bạn đang được quản trị viên duyệt')");
        } catch (\Exception $e) {
            $this->js("alert('{$e->getMessage()}')");
        }
    }

    public function with()
    {
        return [
            'reviews' => \App\Models\Review::query()
                ->where('kol_id', $this->kol->id)
                ->where('status', 1)
                ->orderBy('created_at', 'desc')
                ->paginate(5),
        ];
    }
}
?>
<div>
    <div class="about-section mt-4 mb-4">
        <h2 class="section-title">Đánh giá ({{$reviews->total()}})</h2>
        @foreach($reviews as $v)
            <div class="review mt-3">
                <div class="rating-about">
                    <div>
                        <p>{{$v->customer_name}}</p>
                        <small class="mt-2 text-sm d-inline-block">
                            {{$v->created_at}}
                        </small>
                    </div>
                    <div class="rateting-style">
                        @for($i = 0; $i < $v->rating; $i++)
                            <i class="fas fa-star"></i>
                        @endfor
                    </div>
                </div>
                <div class="rating-content mt-2">
                    <p>{{$v->content}}</p>
                </div>
            </div>
        @endforeach
        <div class="mt-2">
            {{$reviews->links('vendor.pagin')}}
        </div>
    </div>
    <div class="about-section mt-4 mb-4">
        <h2 class="section-title">Để lại 1 đánh giá</h2>
        <form wire:submit="submitRating" class="review_section" method="post">
            <div class="input_wrap for_brands">

                <ul>
                    <li class="essential">
                        <label for="" class="font_semibold eng_font">Tên</label>
                        <input type="text" wire:model="name">
                    </li>
                    <li class="essential">
                        <label for="" class="font_semibold eng_font">Đánh giá</label>
                        <select id="filter-status" class="filter-select-rating w-100"
                                wire:model="rating">
                            <option value="1">1 Sao</option>
                            <option value="2">2 Sao</option>
                            <option value="3">3 Sao</option>
                            <option value="4">4 Sao</option>
                            <option value="5">5 Sao</option>

                        </select>
                    </li>
                    <li class="essential">
                        <label for="" class="font_semibold eng_font">Nội dung</label>
                        <textarea rows="3" wire:model="content"></textarea>
                    </li>
                </ul>
                <button id="brandSubmit" wire:loading.attr="disabled" wire:target="submitRating">
                    <img src="{{asset('images/icon_submit.svg')}}" alt="submit">
                    <p wire:loading.remove wire:target="submitRating" class="font_bold eng_font">Gửi</p>
                    <p wire:loading wire:target="submitRating" class="font_bold eng_font">Đang gửi...</p>
                </button>
            </div>
        </form>
    </div>

</div>
