<?php

use App\Models\Kol;
use function Livewire\Volt\{state, mount};

state([
    'creators'
]);
mount(function () {
    $this->creators = Kol::query()->orderBy('created_at', 'desc')->limit(10)->get();
});

?>
@push('styles')
    @vite('resources/css/kol-item.css')
@endpush

<div>
    <section class="creator_section">
        <div class="bottom">
            <div class="creator_inner">
                <div class="marquee_wrap">
                    <ul class="creator_wrapper" id="maincreatorList">
                        @foreach($creators as $creator)
                        <li class="creator_slide">
                            <div class="hover_wrap  kol-item">
                                <div class="name_btn_wrap">
                                    <p class="name eng_font font_bold font_pink">{{$creator->name}}</p>
                                    <div class="btn_wrap">
                                    </div>
                                </div>
                                <img src="{{ !empty($creator->avatar) ? \Illuminate\Support\Facades\Storage::url($creator->avatar) : asset('client/assets/img/creator/creator_list/demo.png') }}"
                                     alt="{{$creator->name}}">
                            </div>
                        </li>
                        @endforeach
                    </ul>
                </div>
            </div>

        </div>
        <div class="d-flex justify-content-center mt-3 mt-md-5">
            <a href="{{route('products')}}" wire:navigate class="with_us_btn eng_font font_bold">Xem chi tiết</a>
        </div>
    </section>
</div>
