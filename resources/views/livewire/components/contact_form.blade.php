<?php

use App\Models\Contact;
use App\Notifications\ContactFormSubmitted;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use function Livewire\Volt\{state, mount};
use Livewire\Volt\Component;

new class extends Component {

    public $brand = [];
    public $creator = [];

    public function saveBrand()
    {
        try {
            $this->validate([
                'brand.name' => 'required',
                'brand.email' => 'required|email',
                'brand.phone' => 'required',
                'brand.company' => 'required',
                'brand.help' => 'required',
                'brand.hear' => 'required',
            ], [
                'brand.name.required' => 'Vui lòng nhập tên',
                'brand.email.required' => 'Vui lòng nhập email',
                'brand.phone.required' => 'Vui lòng nhập số điện thoại',
                'brand.company.required' => 'Vui lòng nhập công ty',
                'brand.help.required' => 'Vui lòng nhập lý do',
                'brand.hear.required' => 'Vui lòng nhập cách bạn biết đến chúng tôi',
            ]);
            $contact = Contact::create([
                'type' => Contact::TYPE_BRAND,
                'data' => $this->brand,
            ]);

            // Send notification email
            $this->sendContactNotification($contact);

            $this->reset('brand');
            $this->js('alert("Cảm ơn bạn đã gửi đơn")');
        } catch (\Exception $e) {
            $this->js('alert("Vui lòng nhập đầy đủ thông tin")');
        }
    }

    public function saveCreator()
    {
        try {
            $this->validate([
                'creator.name' => 'required',
                'creator.email' => 'required|email',
                'creator.phone' => 'required',
                'creator.birth' => 'required',
                'creator.gender' => 'required',
                'creator.link' => 'required',
                'creator.reason' => 'required',
            ], [
                'creator.name.required' => 'Vui lòng nhập tên',
                'creator.email.required' => 'Vui lòng nhập email',
                'creator.phone.required' => 'Vui lòng nhập số điện thoại',
                'creator.birth.required' => 'Vui lòng nhập ngày sinh',
                'creator.gender.required' => 'Vui lòng nhập giới tính',
                'creator.link.required' => 'Vui lòng nhập liên kết mạng xã hội',
                'creator.reason.required' => 'Vui lòng nhập lý do',
            ]);
            $contact = Contact::create([
                'type' => Contact::TYPE_CREATOR,
                'data' => $this->creator,
            ]);

            // Send notification email
            $this->sendContactNotification($contact);

            $this->reset('creator');
            $this->js('alert("Cảm ơn bạn đã gửi đơn")');

        } catch (\Exception $e) {
            $this->js('alert("Vui lòng nhập đầy đủ thông tin")');
        }
    }

    protected function sendContactNotification($contact)
    {
        try {
            $adminEmail = config('mail.from.address');
            $subject = $contact->type === Contact::TYPE_BRAND
                ? 'Liên hệ mới từ Thương hiệu: ' . $contact->data['name']
                : 'Liên hệ mới từ Người sáng tạo: ' . $contact->data['name'];

            $data = [
                'contact' => $contact,
                'subject' => $subject
            ];
            Mail::send('emails.contact', $data, function ($message) use ($adminEmail, $subject, $contact) {
                $message->to($adminEmail)
                    ->subject($subject);
            });
        } catch (\Exception $e) {
            \Log::error('Failed to send contact notification: ' . $e->getMessage());
        }
    }
}
?>

?>
<div class="right" x-data="{tab: 'brands'}">
    <div class="button_wrap">
        <button id="brandsButton" class="eng_font font_bold" @click="tab = 'brands'"
                :class="{ 'on': tab == 'brands' }">
            Thương hiệu
        </button>
        <button id="creatorsButton" class="eng_font font_bold" @click="tab = 'creators'"
                :class="{ 'on': tab == 'creators' }">
            Người sáng tạo
        </button>
    </div>
    <form wire:submit="saveBrand" method="post">
        <div class="input_wrap for_brands" x-cloak x-show="tab === 'brands'">

            <ul>
                <li class="essential">
                    <label for="" class="font_semibold eng_font">Tên</label>
                    <input type="text" wire:model="brand.name" name="" id="brand_name">
                </li>
                <li class="essential">
                    <label for="" class="font_semibold eng_font">Email</label>
                    <input type="text" wire:model="brand.email" name="" id="brand_email">
                </li>
                <li class="essential">
                    <label for="" class="font_semibold eng_font">Số điện thoại liên lạc</label>
                    <input type="text" wire:model="brand.phone" name="" id="brand_phone">
                </li>
                <li class="essential">
                    <label for="" class="font_semibold eng_font">Công ty</label>
                    <input type="text" wire:model="brand.company" name="" id="brand_company">
                </li>
                <li class="essential">
                    <label for="" class="font_semibold eng_font">Chúng tôi có thể giúp gì cho bạn?</label>
                    <input type="text" wire:model="brand.help" name="" id="brand_help">
                </li>
                <li class="essential">
                    <label for="" class="font_semibold eng_font">Bạn biết đến chúng tôi như thế nào?</label>
                    <input type="text" wire:model="brand.hear" name="" id="brand_hear">
                </li>
            </ul>

            <button id="brandSubmit" wire:loading.attr="disabled" wire:target="saveBrand">

                <img src="{{asset('images/icon_submit.svg')}}" alt="submit">
                <p wire:loading.remove wire:target="saveBrand" class="font_bold eng_font">Gửi</p>
                <p wire:loading wire:target="saveBrand" class="font_bold eng_font">Đang gửi...</p>

            </button>
        </div>
    </form>
    <form wire:submit="saveCreator" method="post">
        <div class="input_wrap for_creators" x-cloak x-show="tab === 'creators'">

            <ul>
                <li class="essential">
                    <label for="" class="font_semibold eng_font">Tên</label>
                    <input wire:model="creator.name" type="text" name="" id="creator_name">
                </li>
                <li class="essential">
                    <label for="" class="font_semibold eng_font">Email</label>
                    <input wire:model="creator.email" type="text" name="" id="creator_email">
                </li>
                <li class="essential">
                    <label for="" class="font_semibold eng_font">Số điện thoại liên lạc</label>
                    <input wire:model="creator.phone" type="text" name="" id="creator_phone">
                </li>
                <li class="essential">
                    <label for="" class="font_semibold eng_font">Ngày sinh</label>
                    <input wire:model="creator.birth" type="text" name="" id="creator_birth">
                </li>
                <li class="essential">
                    <label for="" class="font_semibold eng_font">Giới tính</label>
                    <!-- <input type="text" name="" id=""> -->
                    <ul class="radio_wrap">
                        <li>
                            <input type="radio" wire:model="creator.gender" name="creator_gender"
                                   id="creator_gender_male" value="Male"
                            >
                            <label for="creator_gender_male" class="eng_font font_semibold">Nam</label>
                        </li>
                        <li>
                            <input type="radio" wire:model="creator.gender" name="creator_gender"
                                   id="creator_gender_female" value="Female">
                            <label for="creator_gender_female" class="eng_font font_semibold">Nữ</label>
                        </li>
                        <li>
                            <input type="radio" wire:model="creator.gender" name="creator_gender"
                                   id="creator_gender_other" value="Other">
                            <label for="creator_gender_other" class="eng_font font_semibold">Khác</label>
                        </li>
                    </ul>
                </li>
                <li class="essential">
                    <label for="" class="font_semibold eng_font">Vui lòng cung cấp liên kết mạng xã hội của
                        bạn</label>
                    <input type="text" name="" wire:model="creator.link" id="creator_link"
                           placeholder="TikTok, Instagram, Youtube...">
                </li>
                <li class="essential">
                    <label for="" class="font_semibold eng_font">Vui lòng cho chúng tôi biết lý do bạn nộp
                        đơn</label>
                    <input type="text" name="" wire:model="creator.reason" id="creator_reason">
                </li>
                <li class="">
                    <label for="" class="font_semibold eng_font">Bạn biết đến chúng tôi như thế nào?</label>
                    <input type="text" name="" wire:model="creator.hear" id="creator_hear">
                </li>
            </ul>
            <button id="creatorSubmit" wire:loading.attr="disabled" wire:target="saveCreator">
                <img src="{{asset('images/icon_submit.svg')}}" alt="submit">
                <p wire:loading.remove wire:target="saveCreator" class="font_bold eng_font">Gửi</p>
                <p wire:loading wire:target="saveCreator" class="font_bold eng_font">Đang gửi...</p>
            </button>
        </div>
    </form>
</div>
