<?php
use function Livewire\Volt\{state, mount};

state([
    'partners'
]);
mount(function () {
    $this->partners = \App\Models\Partner::query()->select('name','thumb','link')->orderBy('created_at', 'desc')->get();
});

?>
<div>
    <section class="logo_section">
        <div class="center_wrap top">
            <div class="about_wrap">
                <p class="font_semibold eng_font">Đ<PERSON><PERSON>c tin cậy bởi các thương hiệu toàn cầu</p>
                <span></span>
            </div>
        </div>
        <div class="bottom">
            <div class="swiper logo_swiper">
                <div class="swiper-wrapper">
                    @foreach($partners as $partner)
                        <div class="swiper-slide">
                            <img src="{{Storage::url($partner->thumb)}}" alt="{{$partner->name}}">
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>
</div>
