<div>
    <div class="payment-container">
        <h1 class="section-title">Thanh toán đặt lịch</h1>

        @if (session()->has('message'))
            <div class="alert alert-success">
                {{ session('message') }}
            </div>
        @endif

        @if (session()->has('error'))
            <div class="alert alert-danger">
                {{ session('error') }}
            </div>
        @endif

        @if(!$booking)
            <div class="no-booking-container">
                <div class="alert alert-warning">
                    <h3>Không tìm thấy thông tin đặt lịch</h3>
                    <p>Vui lòng quay lại trang chủ và đặt lịch lại.</p>
                    <p>Bạn sẽ được chuyển hướng tự động trong giây lát...</p>
                </div>
            </div>
        @else
            <div class="payment-grid">
            <!-- Cột trái - Thông tin khách hàng & Phương thức thanh toán -->
            <div class="payment-left">
                <form id="booking-form" wire:submit.prevent="confirmOrder">
                    <!-- Thông tin khách hàng -->
                    <h2 class="method-title">Thông tin khách hàng</h2>
                    <div class="form-group">
                        <label for="fullName" class="form-label">Họ và tên</label>
                        <input type="text" id="fullName" class="form-control" placeholder="Nhập họ và tên của bạn" wire:model="fullName">
                        @error('fullName') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>

                    <div class="form-group">
                        <label for="phone" class="form-label">Số điện thoại</label>
                        <input type="tel" id="phone" class="form-control" placeholder="Nhập số điện thoại của bạn" wire:model="phone">
                        @error('phone') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" id="email" class="form-control" placeholder="Nhập email của bạn" wire:model="email">
                        @error('email') <span class="text-danger">{{ $message }}</span> @enderror
                    </div>

                    <!-- Tổng đơn hàng -->
                    <div class="payment-status">
                        <div class="status-row">
                            <span class="status-label">Tổng đơn hàng:</span>
                            <span class="status-value highlight">{{ $totalAmount }}</span>
                        </div>
                    </div>

                    <!-- Thông tin chuyển khoản -->
                    <h2 class="method-title">Thông tin chuyển khoản</h2>
                    <div class="bank-info">
                        <div class="bank-details">
                            <div class="bank-row">
                                <span class="bank-label">Ngân hàng</span>
                                <div class="bank-value">{{get_config('bank_name')}}</div>
                            </div>
                            <div class="bank-row">
                                <span class="bank-label">Tên tài khoản</span>
                                <div class="bank-value">{{get_config('bank_account_name')}}</div>
                            </div>

                            <div class="bank-row">
                                <span class="bank-label">Số tài khoản</span>
                                <div class="bank-value">{{get_config('bank_account')}}</div>
                            </div>
                            <div class="bank-row">
                                <span class="bank-label">Mã đặt lịch</span>
                                <div class="bank-value highlight">{{ $bookingCode }}</div>
                            </div>
                            <div class="bank-row">
                                <span class="bank-label">Nội dung chuyển khoản</span>
                                <div class="bank-value highlight">{{ $bookingCode }}</div>
                            </div>
                            <div class="bank-row">
                                <span class="bank-label">Số tiền</span>
                                <div class="bank-value highlight">{{ $amountToPay }}</div>
                            </div>
                        </div>

                        <div class="qr-code">
                            <div class="qr-title">Quét mã QR để thanh toán</div>
                            <img src="https://img.vietqr.io/image/{{get_config('bank_code')}}-{{get_config('bank_account')}}-compact2.png?amount={{ $rawAmount }}&addInfo={{ urlencode($bookingCode) }}&accountName={{get_config('bank_account_name')}}" alt="Mã QR thanh toán" class="qr-code-image">
                        </div>
                    </div>
                </form>
            </div>

            <!-- Cột phải - Thông tin đơn hàng -->
            <div class="payment-right">
                <h2 class="method-title">Thông tin đơn hàng</h2>

                <div class="order-summary">
                    <div class="order-item">
                        <div class="item-details">
                            <div class="item-name mb-2">Thuê {{ $kolName }}</div>
                            <div class="item-description mb-2">
                                <strong>Thời gian:</strong> {{ $bookingTime }}, {{ $bookingDate }}
                            </div>
                            <div class="item-description">
                                <strong>Mã đặt lịch:</strong> {{ $bookingCode }}
                            </div>
                        </div>
                    </div>

                    <div class="order-total">
                        <div class="total-row">
                            <span>Đơn giá mỗi giờ:</span>
                            <span>{{ number_format($hourlyRate, 0, ',', '.') }}đ</span>
                        </div>
                        <div class="total-row">
                            <span>Số giờ thuê:</span>
                            <span>{{ $bookingHours }} giờ</span>
                        </div>
                        <div class="total-row">
                            <span>Tạm tính:</span>
                            <span>{{ number_format($hourlyRate * $bookingHours, 0, ',', '.') }}đ</span>
                        </div>
                        <div class="total-row">
                            <span>Phí dịch vụ:</span>
                            <span>0đ</span>
                        </div>
                        <div class="total-row final">
                            <span>Tổng thanh toán:</span>
                            <span>{{ $totalAmount }}</span>
                        </div>
                    </div>
                </div>

                <button type="submit" form="booking-form" class="confirm-button" wire:loading.attr="disabled" wire:target="confirmOrder">
                    <span wire:loading.remove wire:target="confirmOrder">Xác nhận đặt lịch</span>
                    <span wire:loading wire:target="confirmOrder" class="loading-spinner">
                        <svg class="animate-spin text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Xác nhận đặt lịch
                    </span>
                </button>
            </div>
        </div>
        @endif
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Xử lý redirect về trang chủ khi không có booking
            Livewire.on('redirect-to-home', () => {
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000); // Delay 2 giây để người dùng đọc thông báo
            });
        });
    </script>
</div>
