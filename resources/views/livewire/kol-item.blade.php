<div class="kol-item">
    <li data-aos="fade-up" class="content aos-init aos-animate"
        data-id="{{ $kol->id }}"
        data-status="{{ $kol->is_active ? 'online' : 'offline' }}">
        <a href="/products/{{ $kol->id }}">
            <div class="hover_wrap">
                <div class="name_btn_wrap">
                    <p class="name eng_font font_bold font_pink">{{ $kol->name }}</p>
                    <span style="width: 50px; text-align: center" class="creator-gender {{ $kol->gender }}">{{ $kol->gender === 'male' ? 'Nam' : ($kol->gender === 'female' ? 'Nữ' : 'Khác') }}</span>
                    <p class="creator-info">{{ Str::limit($kol->bio, 100) }}</p>
{{--                    <div class="creator-price">{{ number_format($kol->hourly_rate, 0, ',', '.') }}đ / giờ</div>--}}
                    <div class="creator-tags">
                        @foreach($kol->categories as $category)
                            <span class="creator-tag">{{ $category }}</span>
                        @endforeach
                    </div>
                </div>
                @if($kol->avatar)
                    <img src="{{ asset('storage/' . $kol->avatar) }}" alt="{{ $kol->name }}">
                @else
                    <img src="{{ asset('client/assets/img/creator/creator_list/demo.png') }}" alt="{{ $kol->name }}">
                @endif
            </div>
        </a>
    </li>

    @push('styles')
{{--    <link rel="stylesheet" href="{{ asset('client/assets/css/kol-item.css') }}">--}}
        @vite('resources/css/kol-item.css')
    @endpush
</div>
