<x-client-layout>
    <x-slot:title>Về chúng tôi</x-slot:title>

    <x-slot:styles>
        <!-- Thêm CSS và Font Awesome -->
        <link rel="stylesheet" href="{{ asset('client/assets/css/about-us.css') }}"/>

    </x-slot:style>

    <!-- Gi<PERSON> nguyên phần top_video -->
    <section class="top_video">
        <h2 class="font_bold">Về chúng tôi</h2>
        <video src="https://pi-corporation.s3.ap-northeast-2.amazonaws.com/creator_header.mp4"
               poster="/client/assets/img/creator/creator_header.png" muted="" autoplay="" playsinline=""
               loop=""></video>
    </section>

    <!-- Phần giới thiệu -->
    <section class="about-section">
        <div class="about-container">
            <h2 class="section-title">V<PERSON> chúng tôi</h2>
            <p class="section-subtitle">Booking Idol là nền tảng kết nối người dùng với c<PERSON>, <PERSON>er, Game thủ và
                những người nổi tiếng khác. Chúng tôi cung cấp dịch vụ đặt lịch trực tuyến để bạn có thể tương tác với
                những người bạn yêu thích.</p>

            <div class="about-row">
                <div class="about-col-left">
                    <div class="about-image">
                        <img src="{{ asset('client/assets/img/creator/creator_list/demo.png') }}" alt="Về chúng tôi">
                    </div>
                </div>
                <div class="about-col-right">
                    <div class="about-content">
                        <h3>Câu chuyện của chúng tôi</h3>
                        <p>Booking Idol được thành lập vào năm 2023 với mục tiêu tạo ra một nền tảng kết nối giữa người
                            hâm mộ và những người nổi tiếng mà họ yêu thích. Chúng tôi nhận thấy rằng trong thời đại số
                            hóa, mọi người đều muốn có những trải nghiệm cá nhân và kết nối thực sự với những người họ
                            ngưỡng mộ.</p>
                        <p>Từ ý tưởng đó, chúng tôi đã xây dựng một nền tảng cho phép người dùng dễ dàng đặt lịch và
                            tương tác với các Idol, Streamer, Game thủ và những người nổi tiếng khác. Chúng tôi cam kết
                            mang đến trải nghiệm tốt nhất cho cả người dùng và những người sáng tạo nội dung trên nền
                            tảng của chúng tôi.</p>
                    </div>
                </div>
            </div>


        </div>
    </section>

    <!-- Phần giá trị cốt lõi -->
    <section class="values-section">
        <div class="about-container">
            <h2 class="section-title">Giá trị cốt lõi</h2>
            <p class="section-subtitle">Những giá trị định hướng mọi quyết định và hành động của chúng tôi</p>

            <div class="values-grid">
                <div class="value-item">
                    <div class="value-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="value-title">An toàn và Bảo mật</h3>
                    <p class="value-text">Chúng tôi đặt sự an toàn và bảo mật của người dùng lên hàng đầu. Mọi thông tin
                        cá nhân đều được bảo vệ nghiêm ngặt.</p>
                </div>
                <div class="value-item">
                    <div class="value-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h3 class="value-title">Tin cậy</h3>
                    <p class="value-text">Chúng tôi xây dựng mối quan hệ dựa trên sự tin cậy và minh bạch với tất cả
                        người dùng và đối tác.</p>
                </div>
                <div class="value-item">
                    <div class="value-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3 class="value-title">Chất lượng</h3>
                    <p class="value-text">Chúng tôi cam kết cung cấp dịch vụ chất lượng cao và trải nghiệm người dùng
                        tuyệt vời.</p>
                </div>
                <div class="value-item">
                    <div class="value-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3 class="value-title">Đam mê</h3>
                    <p class="value-text">Chúng tôi làm việc với đam mê và nhiệt huyết để mang đến những trải nghiệm
                        đáng nhớ.</p>
                </div>
                <div class="value-item">
                    <div class="value-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="value-title">Cộng đồng</h3>
                    <p class="value-text">Chúng tôi xây dựng một cộng đồng mạnh mẽ và hỗ trợ lẫn nhau giữa người dùng và
                        người sáng tạo nội dung.</p>
                </div>
                <div class="value-item">
                    <div class="value-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3 class="value-title">Đổi mới</h3>
                    <p class="value-text">Chúng tôi luôn tìm kiếm những cách mới để cải thiện và phát triển nền tảng của
                        mình.</p>
                </div>
            </div>

            <div style="text-align: center; margin-top: 40px;">
                <a href="{{ route('products') }}" class="cta-button">Xem danh sách Idol</a>
            </div>
        </div>
    </section>

    <!-- Phần ý kiến khách hàng -->
    <section class="testimonials-section">
        <div class="about-container">
            <h2 class="section-title">Ý kiến khách hàng</h2>
            <p class="section-subtitle">Người dùng nói gì về chúng tôi</p>

            <div class="testimonial-slider">
                @php
                    $testimonials = \App\Models\Testimonial::query()->orderBy('created_at', 'desc')->get();
                @endphp
                @foreach($testimonials as $key => $testimonial)
                    <div class="testimonial-item {{$key == 0 ? 'active' : ''}}">
                        <div class="testimonial-avatar">
                            <img src="{{ Storage::url($testimonial->thumb) }}"
                                 alt="{{$testimonial->name}}">
                        </div>
                        <div class="testimonial-content">
                            <p class="testimonial-text">{{$testimonial->content}}</p>
                            <h4 class="testimonial-author">{{$testimonial->name}}</h4>
                            <p class="testimonial-position">{{$testimonial->position}}</p>
                        </div>
                    </div>
                @endforeach



                <!-- Nút điều hướng -->
                <a class="testimonial-prev" onclick="moveTestimonial(-1)">&#10094;</a>
                <a class="testimonial-next" onclick="moveTestimonial(1)">&#10095;</a>

                <!-- Chấm điều hướng -->
                <div class="testimonial-dots">
                    @foreach($testimonials as $key => $testimonial)
                        <span class="testimonial-dot {{$loop->first ? 'active' : ''}}" onclick="currentTestimonial({{$key}})"></span>
                    @endforeach
                </div>
            </div>
        </div>
    </section>

    <!-- Phần kêu gọi hành động -->
    <section class="cta-section">
        <div class="about-container">
            <div class="cta-content">
                <h2 class="cta-title">Sẵn sàng trải nghiệm?</h2>
                <p class="cta-text">Đặt lịch ngay hôm nay và kết nối với những Idol, Streamer và Game thủ yêu thích của
                    bạn. Chúng tôi cam kết mang đến trải nghiệm tuyệt vời nhất.</p>
                <a href="{{ route('products') }}" class="cta-button" wire:navigate>Khám phá ngay</a>
            </div>
        </div>
    </section>
    <x-slot:scripts>
        <!-- Nhúng file JavaScript riêng -->
                <script src="{{ asset('client/assets/js/about-us.js') }}"></script>
{{--        @vite('resources/js/about-us.js')--}}
    </x-slot:scripts>

</x-client-layout>
