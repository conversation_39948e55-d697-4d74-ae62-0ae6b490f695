<x-filament-panels::page.simple>
    <div class="flex justify-center mb-5">
        <img
            src="{{ asset('images/logo.svg') }}"
            alt="{{ config('app.name') }}"
            class="h-20"
        />
    </div>

    @if (filament()->hasRegistration())
        <x-slot name="subheading">
            {{ __('filament-panels::pages/auth/login.actions.register.before') }}

            {{ $this->registerAction }}
        </x-slot>
    @endif

    {{ \Filament\Support\Facades\FilamentView::renderHook(\Filament\View\PanelsRenderHook::AUTH_LOGIN_FORM_BEFORE, scopes: $this->getRenderHookScopes()) }}

    <x-filament-panels::form id="form" wire:submit="authenticate">
        <div class="flex justify-center">
            <h2 class="text-2xl font-bold tracking-tight text-center mb-4">
                {{ __('filament-panels::pages.login.heading') }}
            </h2>
        </div>

        <p class="text-center text-gray-500 mb-6">
            {{ __('filament-panels::pages.login.subheading') }}
        </p>

        {{ $this->form }}

        <x-filament-panels::form.actions
            :actions="$this->getCachedFormActions()"
            :full-width="$this->hasFullWidthFormActions()"
        />
    </x-filament-panels::form>

    {{ \Filament\Support\Facades\FilamentView::renderHook(\Filament\View\PanelsRenderHook::AUTH_LOGIN_FORM_AFTER, scopes: $this->getRenderHookScopes()) }}
</x-filament-panels::page.simple>
