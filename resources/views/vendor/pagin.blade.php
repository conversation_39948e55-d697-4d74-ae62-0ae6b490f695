@if ($paginator->hasPages())
    <style>

        .pagination-container {
            display: flex
        ;
            justify-content: center;
            margin: 40px 0;
        }
        .pagination {
            display: flex
        ;
            list-style: none;
            padding: 0;
            margin: 0;
            gap: 5px;
        }
        .pagination-link.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .pagination-link.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .pagination-item {
            display: inline-block;
        }
        .pagination-link {
            display: flex
        ;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background-color: #222;
            color: #fff;
            border-radius: 50%;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid #333;
        }
        .pagination-link.active {
            background-color: #FF026F;
            border-color: #FF026F;
        }
    </style>
    <div class="pagination-container">
        <ul class="pagination">
            {{-- Nút Previous --}}
            <li class="pagination-item">
                <button
                    class="pagination-link {{ $paginator->onFirstPage() ? 'disabled' : '' }}"
                    @if (!$paginator->onFirstPage()) wire:click="previousPage('{{ $paginator->getPageName() }}')" @endif
                    @if ($paginator->onFirstPage()) disabled @endif
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z"/>
                    </svg>
                </button>
            </li>

            {{-- Tính toán số trang --}}
            @php
                $currentPage = $paginator->currentPage();
                $lastPage = $paginator->lastPage();
                $visiblePages = 5;
                $half = floor($visiblePages / 2);
                $start = max(1, $currentPage - $half);
                $end = min($lastPage, $start + $visiblePages - 1);
                if ($end - $start < $visiblePages - 1) {
                    $start = max(1, $end - $visiblePages + 1);
                }
            @endphp

            {{-- Trang đầu + ... --}}
            @if ($start > 1)
                <li class="pagination-item">
                    <button class="pagination-link" wire:click="gotoPage(1, '{{ $paginator->getPageName() }}')">1</button>
                </li>
                @if ($start > 2)
                    <li class="pagination-item">
                        <span class="pagination-ellipsis">...</span>
                    </li>
                @endif
            @endif

            {{-- Các trang hiển thị --}}
            @for ($i = $start; $i <= $end; $i++)
                <li class="pagination-item">
                    <button
                        class="pagination-link {{ $i == $currentPage ? 'active' : '' }}"
                        @if ($i != $currentPage) wire:click="gotoPage({{ $i }}, '{{ $paginator->getPageName() }}')" @endif
                    >
                        {{ $i }}
                    </button>
                </li>
            @endfor

            {{-- Dấu ... + trang cuối --}}
            @if ($end < $lastPage)
                @if ($end < $lastPage - 1)
                    <li class="pagination-item">
                        <span class="pagination-ellipsis">...</span>
                    </li>
                @endif
                <li class="pagination-item">
                    <button class="pagination-link" wire:click="gotoPage({{ $lastPage }}, '{{ $paginator->getPageName() }}')">{{ $lastPage }}</button>
                </li>
            @endif

            {{-- Nút Next --}}
            <li class="pagination-item">
                <button
                    class="pagination-link {{ !$paginator->hasMorePages() ? 'disabled' : '' }}"
                    @if ($paginator->hasMorePages()) wire:click="nextPage('{{ $paginator->getPageName() }}')" @endif
                    @if (!$paginator->hasMorePages()) disabled @endif
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/>
                    </svg>
                </button>
            </li>
        </ul>
    </div>
@endif
