/* CSS cho trang Về chúng tôi */
.about-section {
    padding: 80px 0;
    background-color: #000;
    color: #fff;
}

body {
    color: #fff;
}

a:hover {
    color: #fff;
}

.about-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.section-title {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 30px;
    text-align: center;
    color: #FF026F;
}

.section-subtitle {
    font-size: 18px;
    text-align: center;
    margin-bottom: 50px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.about-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 80px;
}

.about-col-left {
    flex: 1;
    padding-right: 30px;
}

.about-col-right {
    flex: 1;
    padding-left: 30px;
}

.about-image {
    width: 100%;
    border-radius: 10px;
    overflow: hidden;
}

.about-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.about-image:hover img {
    transform: scale(1.05);
}

.about-content {
    margin-top: 20px;
}

.about-content h3 {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #FF026F;
}

.about-content p {
    margin-bottom: 20px;
    line-height: 1.6;
}

.about-stats {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 40px;
    margin-bottom: 80px;
}

.stat-item {
    text-align: center;
    flex: 1;
    min-width: 200px;
    margin-bottom: 30px;
}

.stat-number {
    font-size: 48px;
    font-weight: bold;
    color: #FF026F;
    margin-bottom: 10px;
}

.stat-text {
    font-size: 16px;
}

.team-section {
    padding: 80px 0;
    background-color: #111;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 50px;
}

.team-member {
    text-align: center;
}

.team-image {
    width: 100%;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
}

.team-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.team-image:hover img {
    transform: scale(1.05);
}

.team-name {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 5px;
    color: #FF026F;
}

.team-position {
    font-size: 14px;
    color: #999;
    margin-bottom: 15px;
}

.team-social {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.team-social a {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #222;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    transition: all 0.3s ease;
}

.team-social a:hover {
    background-color: #FF026F;
}

.values-section {
    padding: 80px 0;
    background-color: #000;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 50px;
}

.value-item {
    background-color: #111;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    transition: transform 0.3s ease;
}

.value-item:hover {
    transform: translateY(-10px);
}

.value-icon {
    width: 60px;
    height: 60px;
    background-color: #FF026F;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 24px;
    color: #fff;
}

.value-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #FF026F;
}

.value-text {
    font-size: 14px;
    line-height: 1.6;
}

.cta-section {
    padding: 80px 0;
    background-color: #111;
    text-align: center;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta-title {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #FF026F;
}

.cta-text {
    font-size: 18px;
    margin-bottom: 30px;
    line-height: 1.6;
}

.cta-button {
    display: inline-block;
    background-color: #FF026F;
    color: #fff;
    padding: 15px 30px;
    border-radius: 5px;
    font-weight: bold;
    text-decoration: none;
    transition: all 0.3s ease;
}

.cta-button:hover {
    background-color: #d10058;
    transform: translateY(-3px);
    color: #fff;
}

/* Phần ý kiến khách hàng */
.testimonials-section {
    padding: 80px 0;
    background-color: #111;
}

.testimonial-slider {
    position: relative;
    max-width: 800px;
    margin: 60px auto 0;
}

.testimonial-item {
    background-color: #1a1a1a;
    border-radius: 10px;
    padding: 30px;
    position: relative;
    text-align: center;
    display: none;
    animation: fadeEffect 1s;
}

.testimonial-item.active {
    display: block;
}

@keyframes fadeEffect {
    from {opacity: 0.4}
    to {opacity: 1}
}

.testimonial-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    position: absolute;
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid #111;
}

.testimonial-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.testimonial-content {
    margin-top: 60px;
}

.testimonial-text {
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 20px;
    font-style: italic;
    position: relative;
}

.testimonial-text:before,
.testimonial-text:after {
    content: '\201C';
    font-size: 60px;
    color: #FF026F;
    position: absolute;
    opacity: 0.3;
}

.testimonial-text:before {
    top: -20px;
    left: -15px;
}

.testimonial-text:after {
    content: '\201D';
    bottom: -40px;
    right: -15px;
}

.testimonial-author {
    font-weight: bold;
    font-size: 18px;
    color: #FF026F;
}

.testimonial-position {
    font-size: 14px;
    color: #999;
    margin-top: 5px;
}

/* Nút điều hướng slideshow */
.testimonial-prev,
.testimonial-next {
    cursor: pointer;
    position: absolute;
    top: 50%;
    width: 40px;
    height: 40px;
    margin-top: -20px;
    color: white;
    font-weight: bold;
    font-size: 18px;
    transition: 0.6s ease;
    border-radius: 50%;
    user-select: none;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.testimonial-prev {
    left: -60px;
}

.testimonial-next {
    right: -60px;
}

.testimonial-prev:hover,
.testimonial-next:hover {
    background-color: rgba(255,2,111,0.8);
    color: #fff;
}

/* Chấm điều hướng */
.testimonial-dots {
    text-align: center;
    margin-top: 20px;
}

.testimonial-dot {
    cursor: pointer;
    height: 12px;
    width: 12px;
    margin: 0 5px;
    background-color: #bbb;
    border-radius: 50%;
    display: inline-block;
    transition: background-color 0.6s ease;
}

.testimonial-dot.active,
.testimonial-dot:hover {
    background-color: #FF026F;
}

@media (max-width: 992px) {

    .about-row {
        flex-direction: column;
    }

    .about-col-left, .about-col-right {
        width: 100%;
        padding: 0;
        margin-bottom: 40px;
    }

    .team-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .values-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {

    .team-grid {
        grid-template-columns: 1fr;
    }

    .values-grid {
        grid-template-columns: 1fr;
    }

    .stat-item {
        width: 100%;
    }
}

