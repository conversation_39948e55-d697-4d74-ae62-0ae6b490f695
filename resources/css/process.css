/* CSS cho trang quy trình hoạt động */
body {
    color: #fff;
}

a:hover {
    color: #fff;
}

.process-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.process-section {
    padding: 80px 0;
    background-color: #000;
}

.section-title {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 30px;
    text-align: center;
    color: #FF026F;
}

.section-subtitle {
    font-size: 18px;
    text-align: center;
    margin-bottom: 50px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* Process Overview Styles */
.process-overview {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin: 50px 0 80px;
    position: relative;
    flex-wrap: wrap;
    padding: 0;
}

.process-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 30%;
    position: relative;
    z-index: 2;
}

.process-item-number {
    width: 80px;
    height: 80px;
    background-color: #FF026F;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: bold;
    color: #fff;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

.process-item-title {
    font-size: 20px;
    font-weight: bold;
    color: #FF026F;
    margin-bottom: 10px;
    text-align: center;
}

.process-item-desc {
    font-size: 14px;
    text-align: center;
    line-height: 1.5;
    color: #fff;
}

/* Connecting line between process items */
.process-line {
    position: absolute;
    top: 40px;
    height: 2px;
    background-color: #333;
    z-index: 1;
}

@media (max-width: 992px) {
    .process-overview {
        margin: 30px 0 60px;
    }

    .process-item {
        margin: 0 15px;
        width: 160px;
    }

    .process-item-number {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }

    .process-line {
        top: 30px;
    }
}

@media (max-width: 768px) {
    .process-overview {
        flex-direction: column;
        align-items: center;
    }

    .process-item {
        margin: 30px 0;
        width: 100%;
        max-width: 300px;
    }

    .process-line {
        display: none;
    }
}

/* Tab Navigation */
.process-tabs {
    position: sticky;
    top: 80px; /* Adjust this value based on your menu height */
    background-color: #111;
    z-index: 100;
    padding: 15px 0;
    margin-bottom: 50px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

/* Removed duplicate style */

.tab-container {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
}

.tab-item {
    padding: 12px 25px;
    background-color: #222;
    color: #fff;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    text-align: center;
    border: 1px solid #333;
}

.tab-item:hover {
    background-color: #333;
}

.tab-item.active {
    background-color: #FF026F;
    color: #fff;
    border-color: #FF026F;
}

/* Process Steps */
.process-step {
    padding: 80px 0;
    border-bottom: 1px solid #222;
    scroll-margin-top: 100px; /* Offset for sticky header */
}

.process-step:last-child {
    border-bottom: none;
}

.step-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.step-number {
    width: 60px;
    height: 60px;
    background-color: #FF026F;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    margin-right: 20px;
    flex-shrink: 0;
}

.step-title {
    font-size: 28px;
    font-weight: bold;
    color: #FF026F;
}

.step-content {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    align-items: center;
}

.step-text {
    flex: 1;
    min-width: 300px;
}

.step-description {
    margin-bottom: 20px;
    line-height: 1.6;
}

.step-list {
    margin-left: 20px;
    margin-bottom: 20px;
}

.step-list li {
    margin-bottom: 10px;
    position: relative;
    padding-left: 25px;
}

.step-list li:before {
    content: "•";
    color: #FF026F;
    font-size: 20px;
    position: absolute;
    left: 0;
    top: -2px;
}

.step-image {
    flex: 1;
    min-width: 300px;
    border-radius: 10px;
    overflow: hidden;
}

.step-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.step-image:hover img {
    transform: scale(1.05);
}

/* Reverse layout for even steps */
.process-step:nth-child(even) .step-content {
    flex-direction: row-reverse;
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
    background-color: #111;
    text-align: center;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta-title {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #FF026F;
}

.cta-text {
    font-size: 18px;
    margin-bottom: 30px;
    line-height: 1.6;
}

.cta-button {
    display: inline-block;
    background-color: #FF026F;
    color: #fff;
    padding: 15px 30px;
    border-radius: 5px;
    font-weight: bold;
    text-decoration: none;
    transition: all 0.3s ease;
}

.cta-button:hover {
    background-color: #d10058;
    transform: translateY(-3px);
    color: #fff;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .step-content {
        flex-direction: column !important;
    }

    .step-text, .step-image {
        width: 100%;
    }

    .process-step {
        padding: 60px 0;
    }
}

@media (max-width: 768px) {
    .tab-item {
        padding: 10px 15px;
        font-size: 14px;
    }

    .step-title {
        font-size: 24px;
    }

    .step-number {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .process-step {
        padding: 40px 0;
        scroll-margin-top: 80px;
    }
}

@media (max-width: 576px) {
    .tab-container {
        flex-direction: column;
        align-items: center;
    }

    .tab-item {
        width: 100%;
        max-width: 300px;
    }
}
