/* KOL List Styles */

/* Grid Layout */
#creator-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
}

/* KOL Item */
#creator-list .content {
    width: 100%;
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    margin-bottom: 20px;
    height: 480px;
    transition: transform 0.3s ease;
}

#creator-list .content:hover {
    transform: translateY(-5px);
}

/* Hover Wrap */
.hover_wrap {
    position: relative;
    overflow: hidden;
    height: 100%;
}

/* KOL Image */
.hover_wrap img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    display: block;
}

/* KOL Info Container */
.name_btn_wrap {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 20px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7) 50%, rgba(0, 0, 0, 0.5));
    color: #fff;
    z-index: 1;
    min-height: 150px;
}

/* KOL Name */
.name_btn_wrap .name {
    font-size: 24px;
    margin-bottom: 5px;
    color: #FF026F;
}

/* KOL Gender */
.creator-gender {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff;
}

/* KOL Bio */
.creator-info {
    color: #fff;
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* KOL Price */
.creator-price {
    font-size: 18px;
    font-weight: bold;
    color: #FF026F;
    margin-bottom: 8px;
}

/* KOL Tags Container */
.creator-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

/* KOL Tag */
.creator-tag {
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    display: inline-block;
    margin-bottom: 5px;
}

/* Pagination Container */
.pagination-container {
    display: flex;
    justify-content: center;
    margin: 40px 0;
}

/* Pagination List */
.pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 5px;
}

/* Pagination Item */
.pagination-item {
    display: inline-block;
}

/* Pagination Link */
.pagination-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #222;
    color: #fff;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid #333;
}

/* Pagination Link Hover */
.pagination-link:hover {
    background-color: #333;
    border-color: #FF026F;
}

/* Pagination Link Active */
.pagination-link.active {
    background-color: #FF026F;
    border-color: #FF026F;
}

/* Pagination Link Disabled */
.pagination-link.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Pagination Ellipsis */
.pagination-ellipsis {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    color: #fff;
}

/* Pagination Info */
.pagination-info {
    color: #fff;
    margin-bottom: 20px;
    text-align: right;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    #creator-list {
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
    }
}

@media (max-width: 992px) {
    #creator-list {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    #creator-list .content {
        height: 350px;
    }

    .hover_wrap img {
        height: 350px;
    }
}

@media (max-width: 576px) {
    #creator-list {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    #creator-list .content {
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
        height: 450px;
    }

    .hover_wrap img {
        height: 450px;
    }
}
