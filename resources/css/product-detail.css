.product-detail-container {
    background-color: #000;
    color: #fff;
    padding: 80px 0;
}

.product-header {
    padding: 30px;
    background-color: #111;
    border-radius: 10px;
    margin-bottom: 30px;
    position: relative;
}
.rateting-style {
    color: #ff9948;
    margin-bottom: 5px;
    white-space: nowrap;
}
.rating-about{
    display: flex;
    justify-content: space-between;
}
.review .rating-about small{
    font-size: 12px;
    color: #616770
}
.review .rating-about p{
    color: var(--mainpink);
    font-weight: 600;
}
.rating-content p {
    line-height: 1.3;
}
.review_section .input_wrap>ul>li:not(:last-child) {
    margin-bottom: 20px;
}
.review_section .input_wrap>ul>li {
    display: flex
;
    flex-direction: column;
    width: 100%;
}
.review_section .input_wrap>ul>li>label {
    color: #fff;
    font-size: 14px;
    margin-bottom: 8px;
    display: flex
;
    align-items: center;
}
.review_section .input_wrap>ul>li>input[type="text"],
.review_section .input_wrap>ul>li>textarea {
    width: 100%;
    border: 1px solid #ffffff30;
    border-radius: 3px;
    height: 46px;
    color: #fff;
    padding: 0 14px;
}
.review_section .input_wrap>ul>li>textarea {
    background-color: transparent;
    height: unset !important;
    padding: 5px 14px !important;
}
.review_section .input_wrap>ul {
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
}
.review_section .input_wrap>button {
    margin: 0 auto;
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 38px;
    border-radius: 46px;
    background-color: var(--mainpink);
    width: 238px;
    color: #fff;
    font-size: 18px;
    cursor: pointer;
    transition: all .3s;
}
.filter-select-rating{
    background-color: #222;
    border: 1px solid #333;
    color: #fff;
    padding: 8px 15px;
    border-radius: 5px;
    width: 200px;
}
.middle-content {
    height: 100%;
    border-left: 1px solid #333;
    border-right: 1px solid #333;
}

.price-booking-box {
    height: 100%;
    background-color: rgba(0,0,0,0.1);
    border-radius: 10px;
}

@media (max-width: 767.98px) {
    .middle-content {
        border-left: none;
        border-right: none;
        border-top: 1px solid #333;
        border-bottom: 1px solid #333;
        margin: 20px 0;
        padding-top: 20px;
        padding-bottom: 20px;
    }
}

.product-slideshow {
    width: 100%;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
    height: 300px;
    position: relative;
}

.slide-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.slide {
    display: none;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.slide.active {
    display: block;
    opacity: 1;
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
}

.slide-nav {
    position: absolute;
    bottom: 15px;
    left: 0;
    right: 0;
    text-align: center;
}

.slide-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    margin: 0 5px;
    cursor: pointer;
}

.slide-dot.active {
    background-color: #FF026F;
}

.product-name {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #FF026F;
}

.product-status {
    display: inline-block;
    background-color: #FF026F;
    color: #fff;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 14px;
    margin-bottom: 15px;
    width: fit-content;
}

.product-stats {
    display: flex;
    margin-bottom: 20px;
}

.stat-item {
    margin-right: 30px;
    display: flex;
    align-items: center;
}

.stat-icon {
    margin-right: 10px;
    color: #FF026F;
}

/* Staff category tags */
.category-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.category-tag {
    background-color: #222;
    color: #FF026F;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 14px;
    border: 1px solid #FF026F;
    transition: all 0.3s ease;
}

.category-tag:hover {
    background-color: #FF026F;
    color: #fff;
}

.product-price {
    font-size: 28px;
    font-weight: bold;
    color: #FF026F;
    margin-bottom: 20px;
    text-align: center;
}

.price-label {
    display: block;
    font-size: 14px;
    color: #999;
    margin-bottom: 5px;
    text-align: center;
}

.status-box {
    background-color: #1a1a1a;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
}

.status-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #FF026F;
    border-bottom: 1px solid #333;
    padding-bottom: 5px;
}

.status-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.status-icon {
    width: 20px;
    margin-right: 10px;
    color: #FF026F;
}

.achievement-box {
    background-color: #1a1a1a;
    border-radius: 10px;
    padding: 15px;
}

.achievement-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #333;
}

.achievement-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.achievement-icon {
    min-width: 36px;
    width: 36px;
    height: 36px;
    background-color: #FF026F;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
    font-size: 16px;
    box-shadow: 0 3px 6px rgba(0,0,0,0.2);
    flex-shrink: 0;
}

.achievement-text {
    font-size: 14px;
}

.product-actions {
    display: flex;
}

.action-button {
    background-color: #FF026F;
    color: #fff;
    border: none;
    padding: 10px 25px;
    border-radius: 5px;
    font-weight: bold;
    margin-right: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-button:hover {
    background-color: #d10058;
}

.action-button.secondary {
    background-color: transparent;
    border: 2px solid #FF026F;
    color: #FF026F;
}

.action-button.secondary:hover {
    background-color: rgba(255, 2, 111, 0.1);
}

/* Booking modal styles */
.booking-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.8);
}

.booking-modal.show {
    display: block;
}

.modal-content {
    background-color: #111;
    margin: 5% auto;
    padding: 30px;
    border-radius: 10px;
    width: 80%;
    max-width: 800px;
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {opacity: 0; transform: translateY(-50px);}
    to {opacity: 1; transform: translateY(0);}
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #333;
    padding-bottom: 15px;
}

.modal-title {
    font-size: 24px;
    font-weight: bold;
    color: #FF026F;
}

.close-modal {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-modal:hover {
    color: #FF026F;
}

.modal-body {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    position: relative;
}

.alert {
    width: 100%;
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 15px;
    font-size: 14px;
}

.alert-danger {
    background-color: rgba(255, 0, 0, 0.15);
    color: #ff6b6b;
    border: 1px solid rgba(255, 0, 0, 0.3);
    font-weight: 500;
    line-height: 1.4;
    box-shadow: 0 2px 8px rgba(255, 0, 0, 0.1);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.booking-form {
    flex: 1;
    min-width: 300px;
}

.booking-calendar {
    flex: 1;
    min-width: 300px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: 12px;
    border-radius: 5px;
    border: 1px solid #333;
    background-color: #222;
    color: #fff;
}

.price-display {
    background-color: #222;
    color: #FF026F;
    padding: 12px;
    border-radius: 5px;
    border: 1px solid #333;
    font-weight: bold;
    font-size: 18px;
}

textarea.form-control {
    min-height: 120px;
    resize: vertical;
}

.calendar-container {
    background-color: #222;
    border-radius: 5px;
    padding: 15px;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.calendar-month {
    font-weight: bold;
    font-size: 18px;
}

.calendar-nav {
    display: flex;
    gap: 10px;
}

.calendar-nav-btn {
    background-color: #333;
    border: none;
    color: #fff;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.calendar-nav-btn:hover {
    background-color: #FF026F;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
}

.calendar-day-header {
    text-align: center;
    font-weight: bold;
    padding: 5px;
    color: #999;
}

.calendar-day {
    text-align: center;
    padding: 10px 5px;
    border-radius: 5px;
    cursor: pointer;
    color: #fff;
    position: relative;
}

.calendar-day:hover {
    background-color: rgba(255,2,111,0.2);
}

.calendar-day.active {
    background-color: #FF026F;
    color: #fff;
    font-weight: bold;
}

.calendar-day.today {
    border: 2px solid #FF026F;
    font-weight: bold;
}

.calendar-day.disabled {
    color: #555;
    cursor: not-allowed;
}

.calendar-day.disabled:hover {
    background-color: transparent;
}

.calendar-day.active::after {
    content: '';
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: white;
}

.time-slots {
    margin-top: 20px;
}

.time-slot-title {
    font-weight: bold;
    margin-bottom: 10px;
}

.time-slot-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
    margin-bottom: 15px;
}

.time-slot {
    text-align: center;
    padding: 10px 6px;
    border-radius: 6px;
    background-color: #333;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    min-height: 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 14px;
}

.time-slot:hover {
    background-color: #444;
}

.time-slot.active {
    background-color: #FF026F;
    color: #fff;
    font-weight: bold;
    border: 2px solid rgba(255, 2, 111, 0.8);
    box-shadow: 0 0 0 2px rgba(255, 2, 111, 0.3);
    position: relative;
    transform: scale(1.02);
    transition: all 0.2s ease;
}

.time-slot.active::after {
    content: '\2713'; /* Dấu check */
    position: absolute;
    top: 2px;
    right: 5px;
    font-size: 10px;
}

.time-slot.disabled {
    color: #555;
    cursor: not-allowed;
    background-color: #222;
    opacity: 0.6;
}

.time-slot.booked {
    background-color: rgba(255, 0, 0, 0.2);
    color: #999;
    cursor: not-allowed;
    border: 2px solid rgba(255, 0, 0, 0.3);
}

.time-slot.booked:hover {
    background-color: rgba(255, 0, 0, 0.2);
}

.time-slot-time {
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 4px;
}

.time-slot-duration {
    font-size: 12px;
    color: #ccc;
    font-weight: normal;
}

.time-slot.active .time-slot-duration {
    color: #fff;
}

.booked-label {
    position: absolute;
    bottom: 4px;
    right: 6px;
    font-size: 9px;
    color: #ff6b6b;
    font-weight: bold;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 2px 4px;
    border-radius: 3px;
}

/* Styles cho giao diện chọn giờ mới */
.working-schedule-info {
    background-color: rgba(255, 2, 111, 0.1);
    border: 1px solid rgba(255, 2, 111, 0.3);
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 20px;
    text-align: center;
}

.schedule-range {
    color: #FF026F;
    font-weight: bold;
    margin: 0 5px;
}

.time-selection-section {
    margin-bottom: 25px;
}

.time-selection-section h4 {
    color: #fff;
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: 600;
}

.selected-time-summary {
    background-color: rgba(255, 2, 111, 0.15);
    border: 2px solid rgba(255, 2, 111, 0.4);
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
}

.selected-time-summary .selected-time-info {
    background-color: transparent;
    border: none;
    padding: 5px 0;
    margin: 5px 0;
}

.selected-time-summary .selected-time-label {
    color: #ccc;
}

.selected-time-summary .selected-time-value {
    color: #FF026F;
    font-size: 16px;
    font-weight: bold;
}

.no-slots-message {
    text-align: center;
    padding: 20px;
    color: #999;
    font-style: italic;
}

.selected-time-info {
    background-color: rgba(255, 2, 111, 0.1);
    border: 1px solid rgba(255, 2, 111, 0.3);
    border-radius: 5px;
    padding: 8px 12px;
    margin: 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.selected-time-label {
    font-weight: 500;
    margin-right: 8px;
    color: #ccc;
}

.selected-time-value {
    font-weight: bold;
    color: #FF026F;
    font-size: 16px;
}

/* Hiệu ứng nhấp nháy khi chọn khung giờ */
@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 2, 111, 0.7);
    }
    70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(255, 2, 111, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 2, 111, 0);
    }
}

.pulse-effect {
    animation: pulse 1s ease-in-out;
}

.modal-footer {
    margin-top: 30px;
    text-align: right;
}

.btn-submit {
    background-color: #FF026F;
    color: #fff;
    border: none;
    padding: 12px 25px;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-submit:hover {
    background-color: #d10058;
}

.social-links {
    display: flex;
    margin-top: 0;
}

.social-link {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #222;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    transition: all 0.3s ease;
    color: #fff;
    text-decoration: none;
}

.social-link:hover {
    background-color: #FF026F;
}

.social-link i {
    font-size: 18px;
    color: #fff;
}

.about-section {
    background-color: #1a1a1a;
    border-radius: 10px;
    padding: 20px;
}

.about-text {
    line-height: 1.6;
    margin-bottom: 20px;
}

.gallery-section {
    background-color: #1a1a1a;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
}

/* Slideshow styles */
.slideshow-container {
    position: relative;
    max-width: 100%;
    margin: auto;
}

.slide {
    display: none;
    width: 100%;
    border-radius: 5px;
    overflow: hidden;
}

.slide.active {
    display: block;
}

.slide img {
    width: 100%;
    height: 300px;
    object-fit: cover;
}

.gallery-section .slide img {
    width: 100%;
    height: 300px;
    object-fit: cover;
}

.product-slideshow .prev, .product-slideshow .next {
    cursor: pointer;
    position: absolute;
    top: 50%;
    width: auto;
    margin-top: -22px;
    padding: 16px;
    color: white;
    font-weight: bold;
    font-size: 18px;
    transition: 0.3s ease;
    border-radius: 0 3px 3px 0;
    user-select: none;
    background-color: rgba(0,0,0,0.5);
    z-index: 10;
}

.product-slideshow .next {
    right: 0;
    border-radius: 3px 0 0 3px;
}

.product-slideshow .prev:hover, .product-slideshow .next:hover {
    background-color: rgba(255,2,111,0.8);
}

.slide-dots {
    text-align: center;
    margin-top: 15px;
}

.dot {
    cursor: pointer;
    height: 12px;
    width: 12px;
    margin: 0 5px;
    background-color: #bbb;
    border-radius: 50%;
    display: inline-block;
    transition: background-color 0.6s ease;
}

.active-dot, .dot:hover {
    background-color: #FF026F;
}

.section-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #FF026F;
    border-bottom: 1px solid #333;
    padding-bottom: 8px;
}

.schedule-box, .service-box, .services-box {
    background-color: #1a1a1a;
    border-radius: 10px;
    padding: 15px;
}

.services-list {
    margin-top: 10px;
}

.service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #333;
}

.service-item:last-child {
    border-bottom: none;
}

.service-name {
    font-weight: 500;
    color: #fff;
}

.service-price {
    font-weight: 600;
    color: #FF026F;
}

.schedule-item {
    display: flex;
    justify-content: space-between;
    padding: 15px 0;
    border-bottom: 1px solid #333;
}

.schedule-day {
    font-weight: bold;
}

/* Booking Modal */
.booking-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.7);
}

.booking-modal.show {
    display: block;
}

.modal-content {
    background-color: #111;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #333;
    border-radius: 10px;
    width: 80%;
    max-width: 900px;
    color: #fff;
    position: relative;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #333;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.modal-title {
    margin: 0;
    color: #FF026F;
}

.close-modal {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-modal:hover {
    color: #FF026F;
}

.modal-body {
    margin-bottom: 20px;
}

.modal-footer {
    border-top: 1px solid #333;
    padding-top: 15px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.booking-step {
    display: none;
}

.booking-step.active {
    display: block;
}

.booking-step.hidden {
    display: none;
}

/* Form styles */
.form-group {
    margin-bottom: 15px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #333;
    border-radius: 5px;
    background-color: #222;
    color: #fff;
}

.form-control:focus {
    border-color: #FF026F;
    outline: none;
}

/* Price summary */
.price-summary {
    margin-top: 20px;
    background-color: #222;
    border-radius: 5px;
    padding: 15px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.summary-row.total {
    border-top: 1px solid #333;
    padding-top: 10px;
    font-weight: bold;
    color: #FF026F;
}

/* Booking summary */
.booking-summary {
    background-color: #222;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}

.summary-item {
    display: flex;
    margin-bottom: 10px;
}

.summary-label {
    width: 120px;
    font-weight: 500;
}

.summary-value {
    flex: 1;
}

.summary-value.highlight {
    color: #FF026F;
    font-weight: bold;
}

/* Payment method */
.payment-method {
    margin-bottom: 20px;
}

.method-option {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid #333;
    border-radius: 5px;
    margin-bottom: 10px;
    cursor: pointer;
}

.method-option.active {
    border-color: #FF026F;
    background-color: rgba(255, 2, 111, 0.1);
}

.method-radio {
    margin-right: 10px;
}

.method-label {
    font-weight: 500;
    cursor: pointer;
}

/* Bank info */
.bank-info {
    background-color: #222;
    border-radius: 5px;
    padding: 15px;
    margin-top: 15px;
}

.bank-row {
    display: flex;
    margin-bottom: 10px;
}

.bank-label {
    width: 150px;
    font-weight: 500;
    color: #aaa;
}

.bank-value {
    flex: 1;
}

.bank-value.highlight {
    color: #FF026F;
    font-weight: bold;
}

.section-title {
    margin-top: 30px;
    margin-bottom: 15px;
    color: #FF026F;
    font-size: 18px;
}

.btn-back {
    background-color: #333;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-back:hover {
    background-color: #444;
}

.btn-submit {
    background-color: #FF026F;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-submit:hover {
    background-color: #d0025c;
}

.text-danger {
    color: #FF026F;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

.alert {
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 15px;
}

.alert-danger {
    background-color: rgba(255, 0, 0, 0.1);
    border: 1px solid rgba(255, 0, 0, 0.3);
    color: #ff6b6b;
}

/* Loading spinner styles */
.loading-spinner {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner svg {
    width: 16px !important;
    height: 16px !important;
    margin-right: 8px !important;
    vertical-align: middle;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.btn-submit:disabled, .confirm-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}
