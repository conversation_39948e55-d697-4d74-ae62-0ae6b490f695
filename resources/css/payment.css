/* CSS cho trang thanh toán */
body {
    color: #fff;
    background-color: #000;
}

a:hover {
    color: #fff;
}

.payment-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 50px 15px;
}

.section-title {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 30px;
    color: #FF026F;
}

.payment-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

@media (max-width: 992px) {
    .payment-grid {
        grid-template-columns: 1fr;
    }
}

/* Left Column - Customer Info & Payment Method */
.payment-left {
    background-color: #111;
    border-radius: 10px;
    padding: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    background-color: #222;
    border: 1px solid #333;
    border-radius: 5px;
    color: #fff;
    font-size: 16px;
}

.form-control:focus {
    border-color: #FF026F;
    outline: none;
}

.payment-status {
    margin: 30px 0;
    padding: 20px;
    background-color: #1a1a1a;
    border-radius: 10px;
    border-left: 4px solid #FF026F;
}

.status-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.status-row:last-child {
    margin-bottom: 0;
}

.status-label {
    font-weight: 500;
    color: #ccc;
}

.status-value {
    font-weight: bold;
}

.status-value.highlight {
    color: #FF026F;
}

.payment-method {
    margin-bottom: 30px;
}

.method-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #FF026F;
}

.method-option {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: #1a1a1a;
    border-radius: 5px;
    margin-bottom: 10px;
    cursor: pointer;
    border: 1px solid #333;
}

.method-option.active {
    border-color: #FF026F;
    background-color: rgba(255, 2, 111, 0.1);
}

.method-radio {
    margin-right: 15px;
}

.method-label {
    font-weight: 500;
}

.payment-button {
    display: block;
    width: 100%;
    padding: 15px;
    background-color: #FF026F;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 30px;
}

.payment-button:hover {
    background-color: #d10058;
}

.bank-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-top: 30px;
}

.bank-details {
    background-color: #1a1a1a;
    border-radius: 10px;
    padding: 20px;
}

.bank-row {
    margin-bottom: 15px;
}

.bank-label {
    font-weight: 500;
    color: #ccc;
    margin-bottom: 5px;
    display: block;
}

.bank-value {
    font-weight: bold;
    font-size: 16px;
    word-break: break-all;
}

.bank-value.highlight {
    color: #FF026F;
}

.qr-code {
    background-color: #1a1a1a;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.qr-code img {
    max-width: 100%;
    height: auto;
    border-radius: 5px;
}

.qr-title {
    margin-bottom: 15px;
    font-weight: bold;
    color: #FF026F;
}

/* Right Column - Order Summary */
.payment-right {
    background-color: #111;
    border-radius: 10px;
    padding: 30px;
}

.order-summary {
    margin-bottom: 30px;
}

.order-item {
    display: flex;
    justify-content: space-between;
    padding: 15px 0;
    border-bottom: 1px solid #333;
}

.item-details {
    flex: 1;
}

.item-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.item-description {
    color: #ccc;
    font-size: 14px;
}

.item-price {
    font-weight: bold;
    margin-left: 20px;
}

.order-total {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 2px solid #333;
}

.total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.total-row.final {
    font-size: 20px;
    font-weight: bold;
    color: #FF026F;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #333;
}

.confirm-button {
    display: block;
    width: 100%;
    padding: 15px;
    background-color: #FF026F;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.confirm-button:hover {
    background-color: #d10058;
}

/* Loading spinner styles */
.loading-spinner {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner svg {
    width: 16px !important;
    height: 16px !important;
    margin-right: 8px !important;
    vertical-align: middle;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.confirm-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Alert styles */
.alert {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 1.5;
}

.alert-success {
    background-color: rgba(0, 255, 0, 0.1);
    color: #4ade80;
    border: 1px solid rgba(0, 255, 0, 0.2);
}

.alert-danger {
    background-color: rgba(255, 0, 0, 0.1);
    color: #ff6b6b;
    border: 1px solid rgba(255, 0, 0, 0.2);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #fbbf24;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.no-booking-container {
    text-align: center;
    padding: 40px 20px;
}

.no-booking-container h3 {
    margin-bottom: 15px;
    color: #fbbf24;
}

.no-booking-container p {
    margin-bottom: 10px;
    color: #ccc;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .bank-info {
        grid-template-columns: 1fr;
    }

    .payment-status, .bank-details, .qr-code {
        padding: 15px;
    }

    .payment-left, .payment-right {
        padding: 20px;
    }
}
