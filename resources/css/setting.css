@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&amp;display=swap');
/*@import url("../../../cdn.jsdelivr.net/gh/orioncactus/pretendard%40v1.3.9/dist/web/variable/pretendardvariable.min.css");*/

@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+KR:wght@100..900&amp;family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&amp;display=swap');

/* http://meyerweb.com/eric/tools/css/reset/
   v2.0 | 20110126
   License: none (public domain)
*/

* {
	font-family: "Pretendard Variable", "Montserrat", sans-serif;
	box-sizing: border-box;
	letter-spacing: -0.3px;
	 -webkit-font-smoothing: antialiased;
}

::selection {
	color: #fff;
	background-color: #ff006faf;
}
body.scroll_non {
	overflow: hidden;
	padding-right: var(--scrollbar-width);
}
body.scroll_non header{
	padding-right: var(--scrollbar-width);
}
.eng_font {
	font-family: "Montserrat", sans-serif !important;
}

/* regular 400
medium 500
semibold 600
bold 700
extrabold 800 */

.font_regular {
	font-weight: 400;
}

.font_medium {
	font-weight: 500;
}

.font_semibold {
	font-weight: 600;
}

.font_bold {
	font-weight: 700;
}

.font_extrabold {
	font-weight: 800;
}

/* .roboto-bold {
	font-family: "Roboto", sans-serif;
	font-weight: 700;
	font-style: normal;
} */

body {
	position: relative;
	min-height: 100vh;
}

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
	margin: 0;
	padding: 0;
	border: 0;
	font-size: 100%;
	/* font: inherit; */
	/* font-family: "Pretendard Variable", "Montserrat", sans-serif; */
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
	display: block;
}

body {
	line-height: 1;
	background-color: #000;
	position: relative;
	/* 1920~ */
	/* padding-bottom: 304px; */
}
ol,
ul {
	list-style: none;
}

blockquote,
q {
	quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
	content: '';
	content: none;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

a {
	text-decoration: none;
	color: inherit;
}

.center_wrap {
	max-width: 1200px;
	width: 100%;
	padding: 0 10px;
	margin: 0 auto;
}

:root {
	--mainpink: #FF006E;
	--secondpink: #FF358D;
}

.font_pink {
	color: var(--mainpink);
}

button {
	outline: 0;
	border: 0;
	background-color: transparent;
	padding: 0;
}

input {
	border: 0;
	outline: none;
	background-color: transparent;
}

@media screen and (max-width:1200px) {
	.center_wrap {
		padding: 0 32px;
	}
}

@media screen and (max-width:980px) {
	.center_wrap {
		padding: 0 20px;
	}
}


.skiptranslate {
	display: none !important;
}
