/* Banner */
.banner {
    background-color: #111;
    padding: 60px 0;
    text-align: center;
    margin-bottom: 40px;
}

.banner-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.banner h1 {
    font-size: 48px;
    font-weight: bold;
    color: #FF026F;
    margin-bottom: 10px;
}

.banner p {
    font-size: 18px;
    color: #fff;
}

/* Filter */
.filter-container {
    max-width: 1200px;
    margin: 0 auto 40px;
    padding: 20px;
    background-color: #1a1a1a;
    border-radius: 10px;
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #333;
    padding-bottom: 10px;
}

.filter-header h2 {
    font-size: 24px;
    font-weight: bold;
    color: #FF026F;
}

.filter-reset {
    background-color: #333;
    color: #fff;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.filter-reset:hover {
    background-color: #555;
}

.filter-body {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.filter-row {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.filter-label {
    font-size: 16px;
    font-weight: bold;
    color: #fff;
}

.filter-content {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.filter-input, .filter-select {
    background-color: #333;
    color: #fff;
    border: none;
    padding: 10px;
    border-radius: 5px;
    width: 100%;
}

.filter-input::placeholder {
    color: #aaa;
}

.filter-tag {
    background-color: #333;
    color: #fff;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.filter-tag:hover {
    background-color: #555;
}

.filter-tag.active {
    background-color: #FF026F;
    color: #fff;
}

.filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

/* Creator List */
.creator-list-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.creator-list-container h2 {
    font-size: 24px;
    font-weight: bold;
    color: #FF026F;
    margin-bottom: 20px;
    border-bottom: 1px solid #333;
    padding-bottom: 10px;
}

.creator-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    list-style: none;
    padding: 0;
}

.creator-list .content {
    background-color: #1a1a1a;
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.3s;
    cursor: pointer;
}

.creator-list .content:hover {
    transform: translateY(-5px);
}

.hover_wrap {
    padding: 15px;
    position: relative;
}

.name_btn_wrap {
    margin-bottom: 10px;
}

.name {
    font-size: 20px;
    font-weight: bold;
    color: #FF026F;
    margin-bottom: 5px;
}

.creator-gender {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    margin-right: 5px;
}

.creator-gender.male {
    background-color: #3498db;
    color: #fff;
}

.creator-gender.female {
    background-color: #e74c3c;
    color: #fff;
}

.creator-gender.other {
    background-color: #9b59b6;
    color: #fff;
}

.creator-status {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
}

.creator-status.online {
    background-color: #2ecc71;
    color: #fff;
}

.creator-status.offline {
    background-color: #7f8c8d;
    color: #fff;
}

.creator-categories {
    margin-bottom: 10px;
}

.creator-category {
    display: inline-block;
    background-color: #333;
    color: #fff;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    margin-right: 5px;
    margin-bottom: 5px;
}

.creator-services {
    margin-bottom: 10px;
}

.creator-service {
    display: inline-block;
    background-color: #2980b9;
    color: #fff;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    margin-right: 5px;
    margin-bottom: 5px;
}

.creator-price {
    font-size: 16px;
    font-weight: bold;
    color: #FF026F;
    margin-bottom: 10px;
}

.creator-img {
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.creator-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-results {
    grid-column: 1 / -1;
    text-align: center;
    padding: 30px;
    background-color: #1a1a1a;
    border-radius: 10px;
    color: #fff;
}

/* Responsive */
@media (min-width: 768px) {
    .filter-row {
        flex-direction: row;
        align-items: center;
    }
    
    .filter-label {
        width: 150px;
        flex-shrink: 0;
    }
    
    .filter-content {
        flex: 1;
    }
}
