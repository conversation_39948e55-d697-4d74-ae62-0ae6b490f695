document.addEventListener('DOMContentLoaded', function() {
    // Xử lý sự kiện click cho các tag thể loại và dịch vụ
    const filterTags = document.querySelectorAll('.filter-tag');
    filterTags.forEach(tag => {
        tag.addEventListener('click', function() {
            this.classList.toggle('active');
        });
    });

    // Xử lý sự kiện reset
    const resetButton = document.getElementById('filter-reset');
    if (resetButton) {
        resetButton.addEventListener('click', function() {
            // Reset các input
            document.querySelectorAll('.filter-input').forEach(input => {
                input.value = '';
            });

            // Reset các select
            document.querySelectorAll('.filter-select').forEach(select => {
                select.selectedIndex = 0;
            });

            // Reset các tag
            document.querySelectorAll('.filter-tag').forEach(tag => {
                tag.classList.remove('active');
            });
        });
    }

    // Xử lý sự kiện filter
    const filterButton = document.getElementById('filter-apply');
    if (filterButton) {
        filterButton.addEventListener('click', function() {
            // Lấy giá trị từ các trường input
            const searchValue = document.getElementById('filter-search').value.toLowerCase();
            const statusValue = document.getElementById('filter-status').value;

            // Lấy các tag thể loại đã chọn
            const selectedTags = [];
            document.querySelectorAll('.filter-tag.active[data-tag]').forEach(tag => {
                selectedTags.push(tag.getAttribute('data-tag'));
            });

            // Lấy các tag dịch vụ đã chọn
            const selectedServices = [];
            document.querySelectorAll('.filter-tag.active[data-service]').forEach(tag => {
                selectedServices.push(tag.getAttribute('data-service'));
            });

            // Lọc danh sách sản phẩm
            const products = document.querySelectorAll('#creator-list .content');
            products.forEach(product => {
                const name = product.querySelector('.name').textContent.toLowerCase();
                const status = product.getAttribute('data-status');
                const tags = product.getAttribute('data-tags') ? product.getAttribute('data-tags').split(',') : [];
                const services = product.getAttribute('data-services') ? product.getAttribute('data-services').split(',') : [];

                let showProduct = true;

                // Lọc theo tên
                if (searchValue && !name.includes(searchValue)) {
                    showProduct = false;
                }

                // Lọc theo trạng thái
                if (statusValue && status !== statusValue) {
                    showProduct = false;
                }

                // Lọc theo tag thể loại
                if (selectedTags.length > 0) {
                    let hasTag = false;
                    for (let i = 0; i < selectedTags.length; i++) {
                        if (tags.includes(selectedTags[i])) {
                            hasTag = true;
                            break;
                        }
                    }
                    if (!hasTag) {
                        showProduct = false;
                    }
                }

                // Lọc theo dịch vụ thuê
                if (selectedServices.length > 0 && showProduct) {
                    let hasService = false;
                    for (let i = 0; i < selectedServices.length; i++) {
                        if (services.includes(selectedServices[i])) {
                            hasService = true;
                            break;
                        }
                    }
                    if (!hasService) {
                        showProduct = false;
                    }
                }

                // Hiển thị hoặc ẩn sản phẩm
                if (showProduct) {
                    product.style.display = '';
                } else {
                    product.style.display = 'none';
                }
            });
        });
    }

    // Xử lý sự kiện click vào sản phẩm
    const products = document.querySelectorAll('#creator-list .content');
    products.forEach(product => {
        product.addEventListener('click', function() {
            const productId = this.getAttribute('data-id');
            if (productId) {
                window.location.href = `/products/${productId}`;
            }
        });
    });
});
