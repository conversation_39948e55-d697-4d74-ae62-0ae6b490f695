// scroll to top
function scrollToTop() {
    const scrollDuration = 500;
    const scrollStep = -window.scrollY / (scrollDuration / 15);
    const scrollInterval = setInterval(function () {
        if (window.scrollY != 0) {
            window.scrollBy(0, scrollStep);
        } else {
            clearInterval(scrollInterval);
        }
    }, 15);
}

document.addEventListener("scroll", function() {
    const header = document.querySelector(".header_wrap");
    if (header) {
        if (window.scrollY > 0) {
            header.classList.add("fixed");
        } else {
            header.classList.remove("fixed");
        }
    } else {
        console.warn("Element with class 'header_wrap' not found.");
    }
    // if (window.scrollY > 0) {
    //     header.classList.add("fixed");
    // } else {
    //     header.classList.remove("fixed");
    // }
});

// hamburger menu
function toggleHamburgerMenu(hamburgerButton) {
    // 햄버거 버튼에 menu_on 클래스를 토글
    hamburgerButton.classList.toggle('menu_on');

    // .hamburger_menu_wrap 요소에 menu_on 클래스를 토글
    var hamburgerMenuWrap = document.querySelector('.hamburger_menu_wrap');
    if (hamburgerMenuWrap) {
        hamburgerMenuWrap.classList.toggle('menu_on');
    }

    const ques = document.querySelectorAll('.que');

    // 모든 .que 요소에서 on과 off 클래스를 제거하여 초기 상태로 복구
    ques.forEach(q => {
        q.classList.remove('on');
        q.classList.remove('off');
    });
}

//lamg btm
function LangMenu(LangButton) {
    LangButton.classList.toggle('on');

    // const optionButtons = LangButton.querySelectorAll('.option-btn');
    // const selectedText = LangButton.querySelector('.selected-text');

    // optionButtons.forEach(option => {
    //     option.addEventListener('click', function(event) {
    //         event.stopPropagation();

    //         selectedText.innerHTML = option.innerHTML;

    //         LangButton.classList.remove('on');
    //     });
    // });
}

document.addEventListener('click', function(event) {
    const selectBox = document.querySelector('.lang_selectbox');
    if (selectBox && !selectBox.contains(event.target)) {
        selectBox.classList.remove('on');
    }
});

function changeLang(type) {
    const box = document.getElementById('selected_text');
    const gtcombo = document.querySelector('.goog-te-combo');
    let lang = 'ko';
    if (gtcombo == null) {
    }
    if (type == 'KR') {
        lang = 'ko';
        box.innerHTML = '한국어<span class="eng_font">(KR)';

        if (window.google && google.translate) {
            // 구글 번역기를 리셋하여 한국어로 설정하고, 배너는 숨겨둔 상태
            const translateElement = new google.translate.TranslateElement(
                {pageLanguage: 'ko', includedLanguages: 'ko,en,zh-CN,ja,th'}, 'google_translate_element'
            );

            // 한국어로 변경될 때 번역 배너를 숨기지 않더라도 번역을 비활성화
            gtcombo.value = lang;
            gtcombo.dispatchEvent(new Event('change'));

            // 번역기 초기화 후 페이지를 원래 상태로 되돌리기
            // 'ko'가 선택될 때 구글 번역을 비활성화하도록 강제
            setTimeout(() => {
                // 페이지를 강제로 새로 고침하여 구글 번역 해제
                window.location.reload();
            }, 500); // 잠시 후 페이지 리로드 (구글 번역 상태 초기화)
        }

    } else if (type == 'EN') {
        lang = 'en';
        box.innerHTML = 'ENGLISH<span class="eng_font">(EN)';
    } else if (type == 'CN') {
        lang = 'zh-CN';
        box.innerHTML = '中文<span class="eng_font">(CN)';
    } else if (type == 'JP') {
        lang = 'ja';
        box.innerHTML = '日本語<span class="eng_font">(JP)';
    } else if (type == 'TH') {
        lang = 'th';
        box.innerHTML = 'ภาษาไทย<span class="eng_font">(TH)';
    }
    gtcombo.value = lang;
    gtcombo.dispatchEvent(new Event('change'));
}

// when page is fully loaded
// document.addEventListener('DOMContentLoaded', function() {
//     let loaded = false;
//     function googleTranslateElementInit() {
//         new google.translate.TranslateElement({pageLanguage: 'ko',autoDisplay: false}, 'google_translate_element');
//         console.log('googleTranslateElementInit');
//         loaded = true;
//     }
//     const googleLoad = setInterval(function() {
//         if (!loaded) {
//             googleTranslateElementInit();
//         } else {
//             clearInterval(googleLoad);
//         }
//     }, 1000);
// });

// // 아코디언 메뉴 토글 함수
// function toggleAccordion(question) {
//     // 해당 질문에 'on' 클래스 토글
//     question.classList.toggle('on');

//     // 질문의 다음 형제 요소 (답변)를 가져옴
//     var anw = question.nextElementSibling;

//     // 답변이 현재 표시되어 있으면 숨기고, 그렇지 않으면 표시
//     if (anw.style.display === 'block') {
//         anw.style.display = 'none';
//     } else {
//         anw.style.display = 'block';
//     }

//     // 다른 모든 답변 닫기
//     document.querySelectorAll('.anw').forEach(function (siblingAnw) {
//         if (siblingAnw !== anw) {
//             siblingAnw.style.display = 'none';
//         }
//     });
// }

// // 아코디언 초기화 함수
// function initAccordion() {
//     document.querySelectorAll('.que').forEach(function (question) {
//         question.addEventListener('click', function () {
//             toggleAccordion(question);
//         });
//     });
// }

// // DOM이 완전히 로드된 후에 실행
// document.addEventListener('DOMContentLoaded', function () {
//     initAccordion(); // 초기 DOM 로드 시 아코디언 초기화

//     // MutationObserver를 사용하여 동적 HTML 로딩 감지
//     var observer = new MutationObserver(function (mutations) {
//         mutations.forEach(function (mutation) {
//             // 아코디언 메뉴가 포함된 엘리먼트가 추가된 경우 초기화 함수 호출
//             if (mutation.addedNodes.length > 0) {
//                 initAccordion();
//             }
//         });
//     });

//     // 감시할 대상은 body이며, 자식 노드의 변화를 감시함
//     observer.observe(document.body, {
//         childList: true,
//         subtree: true
//     });
// });

document.addEventListener('click', function(event) {
    // 클릭한 요소가 .que 요소인지 확인
    if (event.target.closest('.que')) {
        const clickedQue = event.target.closest('.que');
        const ques = document.querySelectorAll('.que');

        // 클릭된 .que에 on 클래스가 있으면, 모든 요소를 초기 상태로 돌림
        if (clickedQue.classList.contains('on')) {
            ques.forEach(q => {
                q.classList.remove('on');
                q.classList.remove('off');  // 모든 요소를 초기 상태로 되돌림
            });
        } else {
            // 모든 요소를 초기화한 후, 클릭된 요소에 on 클래스를 추가하고 off 클래스를 제거
            ques.forEach(q => {
                q.classList.add('off');
                q.classList.remove('on');
            });

            // 클릭된 .que에 on 클래스를 추가하고 off 클래스를 제거
            clickedQue.classList.add('on');
            clickedQue.classList.remove('off');
        }
    }
});



document.addEventListener('DOMContentLoaded', () => {
    const targetElement = document.querySelector('.skiptranslate');

    // MutationObserver 옵션 설정
    const config = { attributes: true, attributeFilter: ['style'] };

    // MutationObserver 콜백 함수
    const observerCallback = (mutationsList) => {
        mutationsList.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                const style = mutation.target.style.display;

                if (style === 'none') {
                    // display가 none으로 변경되면 body에 'open' 클래스를 추가
                    document.body.classList.add('open');
                } else {
                    // 그 외의 경우는 body에서 'open' 클래스를 제거
                    document.body.classList.remove('open');
                }
            }
        });
    };

    // MutationObserver 생성
    const observer = new MutationObserver(observerCallback);

    // 관찰 시작
    if (targetElement) {
        observer.observe(targetElement, config);
    }
});
