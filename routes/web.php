<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
})->name('home');

Route::get('/products', function () {
    return view('products');
})->name('products');

Route::get('/about', function () {
    return view('about');
})->name('about');

Route::get('/process', function () {
    return view('process');
})->name('process');

Route::get('/contact', function () {
    return view('contact');
})->name('contact');

Route::get('/payment', function () {
    return view('payment');
})->name('payment');

Route::get('/booking-success/{code?}', function ($code = null) {
    // Nếu không có mã đặt lịch và không có thông báo thành công, chuyển hướng về trang chủ
    if (!$code && !session('message')) {
        return redirect()->route('home');
    }

    // Lấy số tiền từ session hoặc sử dụng giá trị mặc định
    $amount = session('amount') ?: 150000;

    return view('booking-success', [
        'bookingCode' => $code ?: 'BOOKING',
        'amount' => $amount
    ]);
})->name('booking.success');

Route::get('/products/{id}', function ($id) {
    return view('product-detail', ['id' => $id]);
})->name('product.detail');
